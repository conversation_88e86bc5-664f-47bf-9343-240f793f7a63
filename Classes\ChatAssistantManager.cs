using System;
using System.Data;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

/// <summary>
/// 智能聊天助手管理类
/// </summary>
public class ChatAssistantManager
{
    /// <summary>
    /// 处理用户消息并生成AI响应
    /// </summary>
    /// <param name="userMessage">用户消息</param>
    /// <param name="userID">用户ID</param>
    /// <param name="userLocation">用户位置（可选）</param>
    /// <returns>AI助手响应</returns>
    public static async Task<AssistantResponse> ProcessUserMessageAsync(string userMessage, int userID, string userLocation = null)
    {
        try
        {
            // 1. 分析用户意图
            var intentResult = await AIService.AnalyzeIntentAsync(userMessage);
            
            // 2. 根据意图处理不同类型的请求
            switch (intentResult.Intent.ToLower())
            {
                case "route_planning":
                    return await HandleRoutePlanningAsync(userMessage, userID, userLocation, intentResult.Entities);
                
                case "shop_recommendation":
                    return await HandleShopRecommendationAsync(userMessage, userID, userLocation, intentResult.Entities);
                
                case "fault_diagnosis":
                    return await HandleFaultDiagnosisAsync(userMessage, userID, intentResult.Entities);
                
                case "price_inquiry":
                    return await HandlePriceInquiryAsync(userMessage, userID, intentResult.Entities);
                
                case "maintenance_advice":
                    return await HandleMaintenanceAdviceAsync(userMessage, userID, intentResult.Entities);
                
                default:
                    return await HandleGeneralChatAsync(userMessage, userID);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"处理用户消息时出错: {ex.Message}");
            return new AssistantResponse
            {
                Message = "抱歉，我暂时无法处理您的请求，请稍后再试。",
                Intent = "error",
                HasLocationData = false
            };
        }
    }

    /// <summary>
    /// 处理路线规划请求
    /// </summary>
    private static async Task<AssistantResponse> HandleRoutePlanningAsync(string userMessage, int userID, string userLocation, Dictionary<string, string> entities)
    {
        try
        {
            LocationInfo userLocationInfo = null;
            
            // 获取用户位置
            if (!string.IsNullOrEmpty(userLocation))
            {
                userLocationInfo = await AmapService.GeocodeAsync(userLocation);
            }
            else if (entities.ContainsKey("location"))
            {
                userLocationInfo = await AmapService.GeocodeAsync(entities["location"]);
            }

            if (userLocationInfo == null)
            {
                return new AssistantResponse
                {
                    Message = "请提供您的当前位置，这样我就能为您规划最佳路线了。例如：\"我在北京市朝阳区，想去最近的维修店\"",
                    Intent = "route_planning",
                    HasLocationData = false
                };
            }

            // 搜索附近的维修店
            var nearbyShops = await AmapService.SearchNearbyShopsAsync(userLocationInfo.Longitude, userLocationInfo.Latitude);
            var dbShops = ShopManager.GetNearbyShops(userLocationInfo.Longitude, userLocationInfo.Latitude, 10);

            // 合并数据库中的维修店信息
            var recommendedShops = new List<RecommendedShop>();
            
            foreach (DataRow shop in dbShops.Rows)
            {
                if (shop["Longitude"] != DBNull.Value && shop["Latitude"] != DBNull.Value)
                {
                    var shopLon = Convert.ToDouble(shop["Longitude"]);
                    var shopLat = Convert.ToDouble(shop["Latitude"]);
                    var distance = AmapService.CalculateDistance(
                        userLocationInfo.Longitude, userLocationInfo.Latitude,
                        shopLon, shopLat);

                    recommendedShops.Add(new RecommendedShop
                    {
                        ShopID = Convert.ToInt32(shop["ShopID"]),
                        Name = shop["ShopName"].ToString(),
                        Address = shop["Address"].ToString(),
                        Distance = (int)distance,
                        Rating = Convert.ToDouble(shop["Rating"]),
                        BusinessHours = shop["BusinessHours"]?.ToString(),
                        Longitude = shopLon,
                        Latitude = shopLat
                    });
                }
            }

            // 按距离排序，取前3个
            recommendedShops = recommendedShops.OrderBy(s => s.Distance).Take(3).ToList();

            if (recommendedShops.Count == 0)
            {
                return new AssistantResponse
                {
                    Message = "抱歉，在您附近10公里范围内没有找到维修店。建议您扩大搜索范围或联系我们的客服。",
                    Intent = "route_planning",
                    HasLocationData = false
                };
            }

            // 为最近的维修店规划路线
            var nearestShop = recommendedShops.First();
            var route = await AmapService.GetRouteAsync(
                $"{userLocationInfo.Longitude},{userLocationInfo.Latitude}",
                $"{nearestShop.Longitude},{nearestShop.Latitude}");

            // 生成AI响应
            var shopInfo = string.Join("\n", recommendedShops.Select(s => 
                $"• {s.Name} - 距离{s.Distance}米，评分{s.Rating:F1}分"));

            var aiPrompt = $@"用户想要路线规划到维修店。
用户位置：{userLocationInfo.Address}
推荐的维修店：
{shopInfo}

最近维修店的路线信息：
- 距离：{route?.Distance ?? 0}米
- 预计时间：{(route?.Duration ?? 0) / 60}分钟
- 过路费：{route?.Tolls ?? 0}元

请生成一个友好、专业的回复，包含路线建议和维修店推荐。";

            var aiResponse = await AIService.GetCarRepairAdviceAsync(aiPrompt);

            return new AssistantResponse
            {
                Message = aiResponse,
                Intent = "route_planning",
                HasLocationData = true,
                UserLocation = userLocationInfo,
                RecommendedShops = recommendedShops,
                RouteInfo = route
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"处理路线规划时出错: {ex.Message}");
            return new AssistantResponse
            {
                Message = "抱歉，路线规划服务暂时不可用，请稍后再试。",
                Intent = "route_planning",
                HasLocationData = false
            };
        }
    }

    /// <summary>
    /// 处理维修店推荐请求
    /// </summary>
    private static async Task<AssistantResponse> HandleShopRecommendationAsync(string userMessage, int userID, string userLocation, Dictionary<string, string> entities)
    {
        try
        {
            LocationInfo userLocationInfo = null;
            
            if (!string.IsNullOrEmpty(userLocation))
            {
                userLocationInfo = await AmapService.GeocodeAsync(userLocation);
            }
            else if (entities.ContainsKey("location"))
            {
                userLocationInfo = await AmapService.GeocodeAsync(entities["location"]);
            }

            List<RecommendedShop> shops;
            
            if (userLocationInfo != null)
            {
                // 基于位置推荐
                var dbShops = ShopManager.GetNearbyShops(userLocationInfo.Longitude, userLocationInfo.Latitude, 15);
                shops = new List<RecommendedShop>();
                
                foreach (DataRow shop in dbShops.Rows)
                {
                    if (shop["Longitude"] != DBNull.Value && shop["Latitude"] != DBNull.Value)
                    {
                        shops.Add(new RecommendedShop
                        {
                            ShopID = Convert.ToInt32(shop["ShopID"]),
                            Name = shop["ShopName"].ToString(),
                            Address = shop["Address"].ToString(),
                            Distance = Convert.ToInt32(shop["Distance"]),
                            Rating = Convert.ToDouble(shop["Rating"]),
                            BusinessHours = shop["BusinessHours"]?.ToString(),
                            Longitude = Convert.ToDouble(shop["Longitude"]),
                            Latitude = Convert.ToDouble(shop["Latitude"])
                        });
                    }
                }
            }
            else
            {
                // 基于评分推荐
                var dbShops = ShopManager.GetAllShopsForFrontend(1); // 按评分排序
                shops = new List<RecommendedShop>();
                
                foreach (DataRow shop in dbShops.Rows.Cast<DataRow>().Take(5))
                {
                    shops.Add(new RecommendedShop
                    {
                        ShopID = Convert.ToInt32(shop["ShopID"]),
                        Name = shop["ShopName"].ToString(),
                        Address = shop["Address"].ToString(),
                        Rating = Convert.ToDouble(shop["Rating"]),
                        BusinessHours = shop["BusinessHours"]?.ToString(),
                        Longitude = shop["Longitude"] != DBNull.Value ? Convert.ToDouble(shop["Longitude"]) : 0,
                        Latitude = shop["Latitude"] != DBNull.Value ? Convert.ToDouble(shop["Latitude"]) : 0
                    });
                }
            }

            var shopInfo = string.Join("\n", shops.Take(3).Select(s => 
                $"• {s.Name} - {s.Address}，评分{s.Rating:F1}分" + 
                (s.Distance > 0 ? $"，距离{s.Distance}米" : "")));

            var aiPrompt = $@"用户需要维修店推荐。
{(userLocationInfo != null ? $"用户位置：{userLocationInfo.Address}" : "用户未提供具体位置")}
推荐的维修店：
{shopInfo}

请生成一个友好、专业的维修店推荐回复。";

            var aiResponse = await AIService.GetCarRepairAdviceAsync(aiPrompt);

            return new AssistantResponse
            {
                Message = aiResponse,
                Intent = "shop_recommendation",
                HasLocationData = userLocationInfo != null,
                UserLocation = userLocationInfo,
                RecommendedShops = shops.Take(3).ToList()
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"处理维修店推荐时出错: {ex.Message}");
            return new AssistantResponse
            {
                Message = "抱歉，维修店推荐服务暂时不可用，请稍后再试。",
                Intent = "shop_recommendation",
                HasLocationData = false
            };
        }
    }

    /// <summary>
    /// 处理故障诊断请求
    /// </summary>
    private static async Task<AssistantResponse> HandleFaultDiagnosisAsync(string userMessage, int userID, Dictionary<string, string> entities)
    {
        var carModel = entities.ContainsKey("car_model") ? entities["car_model"] : "未知车型";
        var problem = entities.ContainsKey("problem") ? entities["problem"] : userMessage;

        var aiPrompt = $@"用户描述了汽车故障问题。
车型：{carModel}
问题描述：{problem}

请提供专业的故障诊断建议，包括可能的原因、紧急程度、建议的处理方式等。";

        var aiResponse = await AIService.GetCarRepairAdviceAsync(aiPrompt);

        return new AssistantResponse
        {
            Message = aiResponse,
            Intent = "fault_diagnosis",
            HasLocationData = false
        };
    }

    /// <summary>
    /// 处理价格咨询请求
    /// </summary>
    private static async Task<AssistantResponse> HandlePriceInquiryAsync(string userMessage, int userID, Dictionary<string, string> entities)
    {
        var aiResponse = await AIService.GetCarRepairAdviceAsync($"用户咨询维修价格：{userMessage}。请提供专业的价格估算建议。");

        return new AssistantResponse
        {
            Message = aiResponse,
            Intent = "price_inquiry",
            HasLocationData = false
        };
    }

    /// <summary>
    /// 处理保养建议请求
    /// </summary>
    private static async Task<AssistantResponse> HandleMaintenanceAdviceAsync(string userMessage, int userID, Dictionary<string, string> entities)
    {
        var carModel = entities.ContainsKey("car_model") ? entities["car_model"] : "未知车型";
        
        var aiResponse = await AIService.GetCarRepairAdviceAsync($"用户咨询汽车保养建议，车型：{carModel}，具体问题：{userMessage}");

        return new AssistantResponse
        {
            Message = aiResponse,
            Intent = "maintenance_advice",
            HasLocationData = false
        };
    }

    /// <summary>
    /// 处理一般聊天
    /// </summary>
    private static async Task<AssistantResponse> HandleGeneralChatAsync(string userMessage, int userID)
    {
        var aiResponse = await AIService.GetCarRepairAdviceAsync(userMessage);

        return new AssistantResponse
        {
            Message = aiResponse,
            Intent = "general_chat",
            HasLocationData = false
        };
    }
}

/// <summary>
/// 助手响应数据结构
/// </summary>
public class AssistantResponse
{
    public string Message { get; set; }
    public string Intent { get; set; }
    public bool HasLocationData { get; set; }
    public LocationInfo UserLocation { get; set; }
    public List<RecommendedShop> RecommendedShops { get; set; }
    public RouteInfo RouteInfo { get; set; }
}

/// <summary>
/// 推荐维修店信息
/// </summary>
public class RecommendedShop
{
    public int ShopID { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public int Distance { get; set; }
    public double Rating { get; set; }
    public string BusinessHours { get; set; }
    public double Longitude { get; set; }
    public double Latitude { get; set; }
}
