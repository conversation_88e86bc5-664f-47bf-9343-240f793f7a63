using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Chat_ChatMain : System.Web.UI.Page
{
    private int currentUserID;
    private string currentUserType;

    /// <summary>
    /// 检查当前用户是否为管理员
    /// </summary>
    /// <returns>是否为管理员</returns>
    protected bool IsCurrentUserAdmin()
    {
        return currentUserType == "Admin";
    }

    /// <summary>
    /// 获取消息的CSS类名
    /// </summary>
    /// <param name="senderID">发送者ID</param>
    /// <param name="senderType">发送者类型</param>
    /// <returns>CSS类名</returns>
    protected string GetMessageCssClass(object senderID, object senderType)
    {
        string baseClass = "message-item";

        // 判断是否为当前用户发送的消息
        if (Convert.ToInt32(senderID) == currentUserID)
        {
            baseClass += " message-sent";
        }
        else
        {
            baseClass += " message-received";
        }

        // 如果是管理员发送的消息，添加管理员样式
        if (senderType.ToString() == "Admin")
        {
            baseClass += " message-admin";
        }

        return baseClass;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户登录
        if (!User.Identity.IsAuthenticated || Session["UserID"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        currentUserID = Convert.ToInt32(Session["UserID"]);
        currentUserType = Session["UserType"]?.ToString() ?? "";

        // 设置管理员功能面板可见性
        pnlAdminBadge.Visible = IsCurrentUserAdmin();
        pnlAdminFeatures.Visible = IsCurrentUserAdmin();

        if (!IsPostBack)
        {
            LoadChatRooms();
            LoadUsers();
        }
        else
        {
            // 处理异步刷新请求
            string eventTarget = Request["__EVENTTARGET"];
            string eventArgument = Request["__EVENTARGUMENT"];
            
            if (eventArgument == "RefreshMessages")
            {
                RefreshCurrentRoomMessages();
            }
        }
    }

    #region 聊天室管理

    /// <summary>
    /// 加载用户的聊天室列表
    /// </summary>
    private void LoadChatRooms()
    {
        try
        {
            DataTable chatRooms = ChatManager.GetUserChatRooms(currentUserID);
            rptChatRooms.DataSource = chatRooms;
            rptChatRooms.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载聊天室列表失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 聊天室选择事件
    /// </summary>
    protected void rptChatRooms_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "SelectRoom")
        {
            int roomID = Convert.ToInt32(e.CommandArgument);
            SelectChatRoom(roomID);
        }
    }

    /// <summary>
    /// 选择聊天室
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    private void SelectChatRoom(int roomID)
    {
        try
        {
            // 检查用户是否有权限访问该聊天室
            if (!ChatManager.CanUserAccessRoom(currentUserID, roomID))
            {
                ShowMessage("您没有权限访问该聊天室。", "warning");
                return;
            }

            // 如果是公共聊天室，自动加入
            DataTable roomInfo = GetRoomInfo(roomID);
            if (roomInfo.Rows.Count > 0)
            {
                string roomType = roomInfo.Rows[0]["RoomType"].ToString();
                if (roomType == "Public")
                {
                    ChatManager.AddUserToRoom(roomID, currentUserID);
                }
            }

            hfCurrentRoomID.Value = roomID.ToString();
            LoadRoomMessages(roomID);
            UpdateRoomHeader(roomID);
            CheckUserMuteStatus(roomID);

            // 更新最后阅读时间
            ChatManager.UpdateLastReadTime(currentUserID, roomID);

            // 显示聊天界面
            pnlNoRoomSelected.Visible = false;
            pnlMessages.Visible = true;
            pnlChatInput.Visible = true;

            // 刷新聊天室列表以更新未读消息数
            LoadChatRooms();
        }
        catch (Exception ex)
        {
            ShowMessage("选择聊天室失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 获取聊天室信息
    /// </summary>
    private DataTable GetRoomInfo(int roomID)
    {
        string query = "SELECT RoomID, RoomName, RoomType, Description FROM ChatRooms WHERE RoomID = @RoomID";
        return DatabaseHelper.ExecuteQuery(query, new System.Data.SqlClient.SqlParameter("@RoomID", roomID));
    }

    /// <summary>
    /// 更新聊天室标题
    /// </summary>
    private void UpdateRoomHeader(int roomID)
    {
        DataTable roomInfo = GetRoomInfo(roomID);
        if (roomInfo.Rows.Count > 0)
        {
            lblCurrentRoomName.Text = roomInfo.Rows[0]["RoomName"].ToString();
            string roomType = roomInfo.Rows[0]["RoomType"].ToString();
            lblCurrentRoomType.Text = roomType == "Public" ? "公共聊天" : "私人聊天";
            lblCurrentRoomType.CssClass = roomType == "Public" ? "badge badge-success ml-2" : "badge badge-info ml-2";
        }
    }

    /// <summary>
    /// 检查用户禁言状态
    /// </summary>
    private void CheckUserMuteStatus(int roomID)
    {
        bool isMuted = ChatManager.IsUserMuted(currentUserID, roomID);
        pnlMuteNotice.Visible = isMuted;
        pnlChatInput.Visible = !isMuted;

        if (isMuted)
        {
            lblMuteInfo.Text = "，您暂时无法在此聊天室发送消息。";
        }
    }

    #endregion

    #region 消息管理

    /// <summary>
    /// 加载聊天室消息
    /// </summary>
    private void LoadRoomMessages(int roomID)
    {
        try
        {
            DataTable messages = ChatManager.GetRoomMessages(roomID, 50, 0);
            
            // 反转消息顺序，最新的在下面
            DataView dv = messages.DefaultView;
            dv.Sort = "SentDate ASC";
            DataTable sortedMessages = dv.ToTable();

            rptMessages.DataSource = sortedMessages;
            rptMessages.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载消息失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 刷新当前聊天室消息
    /// </summary>
    private void RefreshCurrentRoomMessages()
    {
        if (!string.IsNullOrEmpty(hfCurrentRoomID.Value))
        {
            int roomID = Convert.ToInt32(hfCurrentRoomID.Value);
            LoadRoomMessages(roomID);
            ChatManager.UpdateLastReadTime(currentUserID, roomID);
        }
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    protected void btnSendMessage_Click(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(hfCurrentRoomID.Value))
        {
            ShowMessage("请先选择聊天室。", "warning");
            return;
        }

        string messageContent = txtMessage.Text.Trim();
        if (string.IsNullOrEmpty(messageContent))
        {
            ShowMessage("消息内容不能为空。", "warning");
            return;
        }

        try
        {
            int roomID = Convert.ToInt32(hfCurrentRoomID.Value);
            int result = ChatManager.SendMessage(roomID, currentUserID, messageContent);

            if (result > 0)
            {
                txtMessage.Text = "";
                LoadRoomMessages(roomID);
                LoadChatRooms(); // 刷新聊天室列表
            }
            else if (result == -2)
            {
                ShowMessage("您已被禁言，无法发送消息。", "warning");
                CheckUserMuteStatus(roomID);
            }
            else if (result == -3)
            {
                ShowMessage("您没有权限在此聊天室发送消息。", "warning");
            }
            else
            {
                ShowMessage("发送消息失败，请重试。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("发送消息时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 发送系统公告
    /// </summary>
    protected void btnSystemAnnouncement_Click(object sender, EventArgs e)
    {
        if (!IsCurrentUserAdmin())
        {
            ShowMessage("您没有权限执行此操作。", "danger");
            return;
        }

        string messageContent = txtMessage.Text.Trim();
        if (string.IsNullOrEmpty(messageContent))
        {
            ShowMessage("公告内容不能为空。", "warning");
            return;
        }

        try
        {
            // 向所有公共聊天室发送系统公告
            string announcementMessage = "【系统公告】" + messageContent;
            bool success = ChatManager.SendSystemAnnouncement(currentUserID, announcementMessage);

            if (success)
            {
                txtMessage.Text = "";
                ShowMessage("系统公告发送成功。", "success");
                LoadChatRooms();
                if (!string.IsNullOrEmpty(hfCurrentRoomID.Value))
                {
                    LoadRoomMessages(Convert.ToInt32(hfCurrentRoomID.Value));
                }
            }
            else
            {
                ShowMessage("发送系统公告失败，请重试。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("发送系统公告时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 发送全员广播
    /// </summary>
    protected void btnBroadcastMessage_Click(object sender, EventArgs e)
    {
        if (!IsCurrentUserAdmin())
        {
            ShowMessage("您没有权限执行此操作。", "danger");
            return;
        }

        if (string.IsNullOrEmpty(hfCurrentRoomID.Value))
        {
            ShowMessage("请先选择聊天室。", "warning");
            return;
        }

        string messageContent = txtMessage.Text.Trim();
        if (string.IsNullOrEmpty(messageContent))
        {
            ShowMessage("广播内容不能为空。", "warning");
            return;
        }

        try
        {
            int roomID = Convert.ToInt32(hfCurrentRoomID.Value);
            string broadcastMessage = "【管理员广播】" + messageContent;
            int result = ChatManager.SendMessage(roomID, currentUserID, broadcastMessage);

            if (result > 0)
            {
                txtMessage.Text = "";
                ShowMessage("广播消息发送成功。", "success");
                LoadRoomMessages(roomID);
                LoadChatRooms();
            }
            else
            {
                ShowMessage("发送广播消息失败，请重试。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("发送广播消息时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 消息操作事件
    /// </summary>
    protected void rptMessages_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "DeleteMessage" && IsCurrentUserAdmin())
        {
            try
            {
                int messageID = Convert.ToInt32(e.CommandArgument);
                bool success = ChatManager.DeleteMessage(messageID, currentUserID);

                if (success)
                {
                    ShowMessage("消息已删除。", "success");
                    RefreshCurrentRoomMessages();
                }
                else
                {
                    ShowMessage("删除消息失败。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("删除消息时出错：" + ex.Message, "danger");
            }
        }
    }

    #endregion

    #region 私聊管理

    /// <summary>
    /// 创建私聊按钮点击事件
    /// </summary>
    protected void btnCreatePrivateChat_Click(object sender, EventArgs e)
    {
        LoadUsers();
        ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "$('#createPrivateChatModal').modal('show');", true);
    }

    /// <summary>
    /// 加载用户列表
    /// </summary>
    private void LoadUsers()
    {
        try
        {
            string searchKeyword = txtSearchUser.Text.Trim();
            DataTable users = ChatManager.GetUsersForChat(currentUserID, searchKeyword);
            gvUsers.DataSource = users;
            gvUsers.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载用户列表失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 搜索用户
    /// </summary>
    protected void btnSearchUser_Click(object sender, EventArgs e)
    {
        LoadUsers();
        ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "$('#createPrivateChatModal').modal('show');", true);
    }

    /// <summary>
    /// 用户列表操作事件
    /// </summary>
    protected void gvUsers_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "CreateChat")
        {
            try
            {
                int targetUserID = Convert.ToInt32(e.CommandArgument);
                int roomID = ChatManager.CreatePrivateChatRoom(currentUserID, targetUserID);

                if (roomID > 0)
                {
                    LoadChatRooms();
                    SelectChatRoom(roomID);
                    ShowMessage("私聊创建成功。", "success");
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideModal", "$('#createPrivateChatModal').modal('hide');", true);
                }
                else
                {
                    ShowMessage("创建私聊失败。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("创建私聊时出错：" + ex.Message, "danger");
            }
        }
    }

    #endregion

    #region 刷新和辅助方法

    /// <summary>
    /// 刷新聊天室列表
    /// </summary>
    protected void btnRefreshRooms_Click(object sender, EventArgs e)
    {
        LoadChatRooms();
        if (!string.IsNullOrEmpty(hfCurrentRoomID.Value))
        {
            RefreshCurrentRoomMessages();
        }
    }

    /// <summary>
    /// 获取当前用户ID（用于前端判断）
    /// </summary>
    protected int GetCurrentUserID()
    {
        return currentUserID;
    }



    /// <summary>
    /// 获取用户类型显示名称
    /// </summary>
    protected string GetUserTypeDisplayName(string userType)
    {
        switch (userType)
        {
            case "Admin": return "管理员";
            case "CarOwner": return "车主";
            case "RepairShop": return "维修店";
            default: return userType;
        }
    }

    /// <summary>
    /// 显示消息
    /// </summary>
    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = "alert alert-" + type;
        pnlMessage.Visible = true;
    }

    #endregion
}
