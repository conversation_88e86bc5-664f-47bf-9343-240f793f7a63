using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_PartsManagement : System.Web.UI.Page
{
    private int userID;
    private int shopID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadParts();
        }
    }

    /// <summary>
    /// 加载零件列表
    /// </summary>
    private void LoadParts()
    {
        try
        {
            DataTable parts = PartsManager.GetPartsByShopID(shopID);
            gvParts.DataSource = parts;
            gvParts.DataBind();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载零件列表时出错: {ex.Message}");
            ShowMessage("加载零件列表时出错", "alert-danger");
        }
    }

    /// <summary>
    /// 添加零件按钮点击事件
    /// </summary>
    protected void btnAddPart_Click(object sender, EventArgs e)
    {
        ShowPartForm(true);
    }

    /// <summary>
    /// 显示零件表单
    /// </summary>
    /// <param name="isAdd">是否为添加模式</param>
    private void ShowPartForm(bool isAdd)
    {
        pnlPartForm.Visible = true;
        gvParts.Visible = false;
        lblMessage.Visible = false;

        if (isAdd)
        {
            lblFormTitle.Text = "添加零件";
            hfPartID.Value = "";
            ClearForm();
        }
        else
        {
            lblFormTitle.Text = "编辑零件";
        }
    }

    /// <summary>
    /// 清空表单
    /// </summary>
    private void ClearForm()
    {
        txtPartName.Text = "";
        txtPartNumber.Text = "";
        txtPrice.Text = "";
        txtStockQuantity.Text = "0";
        txtDescription.Text = "";
    }

    /// <summary>
    /// 保存零件按钮点击事件
    /// </summary>
    protected void btnSavePart_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
            return;

        try
        {
            string partName = txtPartName.Text.Trim();
            string partNumber = txtPartNumber.Text.Trim();
            decimal price = Convert.ToDecimal(txtPrice.Text);
            int stockQuantity = Convert.ToInt32(txtStockQuantity.Text);
            string description = txtDescription.Text.Trim();

            bool isEdit = !string.IsNullOrEmpty(hfPartID.Value);
            int partID = isEdit ? Convert.ToInt32(hfPartID.Value) : 0;

            // 检查零件编号是否重复
            if (!string.IsNullOrEmpty(partNumber))
            {
                if (PartsManager.IsPartNumberExists(shopID, partNumber, partID))
                {
                    ShowMessage("零件编号已存在，请使用其他编号", "alert-warning");
                    return;
                }
            }

            bool success;
            if (isEdit)
            {
                success = PartsManager.UpdatePart(partID, partName, partNumber, price, stockQuantity, description);
            }
            else
            {
                int newPartID = PartsManager.AddPart(shopID, partName, partNumber, price, stockQuantity, description);
                success = newPartID > 0;
            }

            if (success)
            {
                pnlPartForm.Visible = false;
                gvParts.Visible = true;
                LoadParts();
                ShowMessage(isEdit ? "零件更新成功" : "零件添加成功", "alert-success");
            }
            else
            {
                ShowMessage(isEdit ? "零件更新失败" : "零件添加失败", "alert-danger");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存零件时出错: {ex.Message}");
            ShowMessage("保存零件时出错", "alert-danger");
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    protected void btnCancelPart_Click(object sender, EventArgs e)
    {
        pnlPartForm.Visible = false;
        gvParts.Visible = true;
        lblMessage.Visible = false;
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvParts_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int partID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "EditPart")
        {
            EditPart(partID);
        }
        else if (e.CommandName == "DeletePart")
        {
            DeletePart(partID);
        }
    }

    /// <summary>
    /// 编辑零件
    /// </summary>
    /// <param name="partID">零件ID</param>
    private void EditPart(int partID)
    {
        try
        {
            DataTable partData = PartsManager.GetPartByID(partID);
            if (partData != null && partData.Rows.Count > 0)
            {
                DataRow part = partData.Rows[0];
                
                hfPartID.Value = partID.ToString();
                txtPartName.Text = part["PartName"].ToString();
                txtPartNumber.Text = part["PartNumber"] != DBNull.Value ? part["PartNumber"].ToString() : "";
                txtPrice.Text = Convert.ToDecimal(part["Price"]).ToString("F2");
                txtStockQuantity.Text = part["StockQuantity"].ToString();
                txtDescription.Text = part["Description"] != DBNull.Value ? part["Description"].ToString() : "";

                ShowPartForm(false);
            }
            else
            {
                ShowMessage("找不到指定的零件", "alert-warning");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"编辑零件时出错: {ex.Message}");
            ShowMessage("编辑零件时出错", "alert-danger");
        }
    }

    /// <summary>
    /// 删除零件
    /// </summary>
    /// <param name="partID">零件ID</param>
    private void DeletePart(int partID)
    {
        try
        {
            bool success = PartsManager.DeletePart(partID);
            if (success)
            {
                LoadParts();
                ShowMessage("零件删除成功", "alert-success");
            }
            else
            {
                ShowMessage("零件删除失败", "alert-danger");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除零件时出错: {ex.Message}");
            ShowMessage("删除零件时出错", "alert-danger");
        }
    }

    /// <summary>
    /// GridView分页事件
    /// </summary>
    protected void gvParts_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvParts.PageIndex = e.NewPageIndex;
        LoadParts();
    }

    /// <summary>
    /// 显示消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="cssClass">CSS类</param>
    private void ShowMessage(string message, string cssClass)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = $"alert {cssClass}";
        lblMessage.Visible = true;
    }
}