using System;
using System.Data;
using System.Text;
using System.Web.UI;

public partial class Chat_TestChat : System.Web.UI.Page
{
    private StringBuilder testResults = new StringBuilder();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            AddTestResult("聊天系统测试页面已加载");
            AddTestResult("当前时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            AddTestResult("----------------------------------------");
        }
    }

    protected void btnTestDatabase_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 数据库连接测试 ===");
            
            // 测试基本数据库连接
            string query = "SELECT COUNT(*) FROM Users";
            object result = DatabaseHelper.ExecuteScalar(query);
            AddTestResult($"用户总数：{result}");

            // 测试聊天相关表
            query = "SELECT COUNT(*) FROM ChatRooms";
            result = DatabaseHelper.ExecuteScalar(query);
            AddTestResult($"聊天室总数：{result}");

            query = "SELECT COUNT(*) FROM ChatMessages";
            result = DatabaseHelper.ExecuteScalar(query);
            AddTestResult($"消息总数：{result}");

            query = "SELECT COUNT(*) FROM ChatRoomMembers";
            result = DatabaseHelper.ExecuteScalar(query);
            AddTestResult($"聊天室成员总数：{result}");

            query = "SELECT COUNT(*) FROM ChatMuteRecords";
            result = DatabaseHelper.ExecuteScalar(query);
            AddTestResult($"禁言记录总数：{result}");

            AddTestResult("数据库连接测试成功！");
            ShowMessage("数据库连接测试完成", "success");
        }
        catch (Exception ex)
        {
            AddTestResult($"数据库连接测试失败：{ex.Message}");
            ShowMessage("数据库连接测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    protected void btnTestChatRooms_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 聊天室查询测试 ===");
            
            // 假设当前用户ID为1（需要根据实际情况调整）
            int testUserID = 1;
            
            DataTable chatRooms = ChatManager.GetUserChatRooms(testUserID);
            AddTestResult($"用户 {testUserID} 可访问的聊天室数量：{chatRooms.Rows.Count}");
            
            foreach (DataRow row in chatRooms.Rows)
            {
                AddTestResult($"- 聊天室：{row["RoomName"]} ({row["RoomType"]})");
            }

            AddTestResult("聊天室查询测试成功！");
            ShowMessage("聊天室查询测试完成", "success");
        }
        catch (Exception ex)
        {
            AddTestResult($"聊天室查询测试失败：{ex.Message}");
            ShowMessage("聊天室查询测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    protected void btnTestUsers_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 用户查询测试 ===");
            
            // 假设当前用户ID为1
            int testUserID = 1;
            
            DataTable users = ChatManager.GetUsersForChat(testUserID);
            AddTestResult($"可创建私聊的用户数量：{users.Rows.Count}");
            
            int displayCount = Math.Min(5, users.Rows.Count);
            for (int i = 0; i < displayCount; i++)
            {
                DataRow row = users.Rows[i];
                AddTestResult($"- 用户：{row["Username"]} ({row["UserType"]}) - {row["Email"]}");
            }
            
            if (users.Rows.Count > 5)
            {
                AddTestResult($"... 还有 {users.Rows.Count - 5} 个用户");
            }

            AddTestResult("用户查询测试成功！");
            ShowMessage("用户查询测试完成", "success");
        }
        catch (Exception ex)
        {
            AddTestResult($"用户查询测试失败：{ex.Message}");
            ShowMessage("用户查询测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    protected void btnTestCreatePrivateChat_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 创建私聊测试 ===");
            
            // 获取前两个用户进行测试
            DataTable users = ChatManager.GetUsersForChat(1);
            if (users.Rows.Count < 2)
            {
                AddTestResult("需要至少2个用户才能测试私聊创建");
                ShowMessage("用户数量不足，无法测试私聊创建", "warning");
                return;
            }

            int user1ID = Convert.ToInt32(users.Rows[0]["UserID"]);
            int user2ID = Convert.ToInt32(users.Rows[1]["UserID"]);
            
            AddTestResult($"尝试在用户 {user1ID} 和用户 {user2ID} 之间创建私聊");
            
            int roomID = ChatManager.CreatePrivateChatRoom(user1ID, user2ID);
            
            if (roomID > 0)
            {
                AddTestResult($"私聊创建成功！聊天室ID：{roomID}");
                
                // 验证聊天室是否真的创建了
                string query = "SELECT RoomName, RoomType FROM ChatRooms WHERE RoomID = @RoomID";
                DataTable roomInfo = DatabaseHelper.ExecuteQuery(query, 
                    new System.Data.SqlClient.SqlParameter("@RoomID", roomID));
                
                if (roomInfo.Rows.Count > 0)
                {
                    AddTestResult($"聊天室信息：{roomInfo.Rows[0]["RoomName"]} ({roomInfo.Rows[0]["RoomType"]})");
                }
                
                ShowMessage("私聊创建测试成功", "success");
            }
            else
            {
                AddTestResult("私聊创建失败");
                ShowMessage("私聊创建测试失败", "danger");
            }
        }
        catch (Exception ex)
        {
            AddTestResult($"创建私聊测试失败：{ex.Message}");
            AddTestResult($"详细错误：{ex.StackTrace}");
            ShowMessage("创建私聊测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    protected void btnTestSendMessage_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 发送消息测试 ===");
            
            // 获取第一个聊天室
            string query = "SELECT TOP 1 RoomID, RoomName FROM ChatRooms WHERE IsActive = 1";
            DataTable rooms = DatabaseHelper.ExecuteQuery(query);
            
            if (rooms.Rows.Count == 0)
            {
                AddTestResult("没有可用的聊天室");
                ShowMessage("没有可用的聊天室进行测试", "warning");
                return;
            }

            int roomID = Convert.ToInt32(rooms.Rows[0]["RoomID"]);
            string roomName = rooms.Rows[0]["RoomName"].ToString();
            
            AddTestResult($"向聊天室 '{roomName}' (ID: {roomID}) 发送测试消息");
            
            // 假设发送者ID为1
            int senderID = 1;
            string testMessage = $"测试消息 - {DateTime.Now:HH:mm:ss}";
            
            int messageID = ChatManager.SendMessage(roomID, senderID, testMessage);
            
            if (messageID > 0)
            {
                AddTestResult($"消息发送成功！消息ID：{messageID}");
                AddTestResult($"消息内容：{testMessage}");
                ShowMessage("消息发送测试成功", "success");
            }
            else if (messageID == -2)
            {
                AddTestResult("发送失败：用户被禁言");
                ShowMessage("用户被禁言，无法发送消息", "warning");
            }
            else if (messageID == -3)
            {
                AddTestResult("发送失败：无权限访问聊天室");
                ShowMessage("无权限访问聊天室", "warning");
            }
            else
            {
                AddTestResult("消息发送失败");
                ShowMessage("消息发送测试失败", "danger");
            }
        }
        catch (Exception ex)
        {
            AddTestResult($"发送消息测试失败：{ex.Message}");
            ShowMessage("发送消息测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    protected void btnTestMuteUser_Click(object sender, EventArgs e)
    {
        try
        {
            AddTestResult("=== 禁言功能测试 ===");
            
            // 获取一个测试用户
            DataTable users = ChatManager.GetUsersForChat(1);
            if (users.Rows.Count == 0)
            {
                AddTestResult("没有可用的用户进行禁言测试");
                ShowMessage("没有可用的用户进行测试", "warning");
                return;
            }

            int testUserID = Convert.ToInt32(users.Rows[0]["UserID"]);
            string testUserName = users.Rows[0]["Username"].ToString();
            
            AddTestResult($"测试用户：{testUserName} (ID: {testUserID})");
            
            // 检查当前禁言状态
            bool isMutedBefore = ChatManager.IsUserMuted(testUserID);
            AddTestResult($"禁言前状态：{(isMutedBefore ? "已禁言" : "正常")}");
            
            if (!isMutedBefore)
            {
                // 执行禁言（1小时）
                DateTime muteEndTime = DateTime.Now.AddHours(1);
                bool muteResult = ChatManager.MuteUser(testUserID, 1, null, "测试禁言", muteEndTime);
                
                if (muteResult)
                {
                    AddTestResult("禁言操作成功");
                    
                    // 验证禁言状态
                    bool isMutedAfter = ChatManager.IsUserMuted(testUserID);
                    AddTestResult($"禁言后状态：{(isMutedAfter ? "已禁言" : "正常")}");
                    
                    if (isMutedAfter)
                    {
                        AddTestResult("禁言功能测试成功！");
                        ShowMessage("禁言功能测试成功", "success");
                    }
                    else
                    {
                        AddTestResult("禁言状态验证失败");
                        ShowMessage("禁言状态验证失败", "warning");
                    }
                }
                else
                {
                    AddTestResult("禁言操作失败");
                    ShowMessage("禁言操作失败", "danger");
                }
            }
            else
            {
                // 解除禁言
                bool unmuteResult = ChatManager.UnmuteUser(testUserID);
                
                if (unmuteResult)
                {
                    AddTestResult("解除禁言操作成功");
                    
                    // 验证禁言状态
                    bool isMutedAfter = ChatManager.IsUserMuted(testUserID);
                    AddTestResult($"解禁后状态：{(isMutedAfter ? "已禁言" : "正常")}");
                    
                    ShowMessage("解除禁言测试成功", "success");
                }
                else
                {
                    AddTestResult("解除禁言操作失败");
                    ShowMessage("解除禁言操作失败", "danger");
                }
            }
        }
        catch (Exception ex)
        {
            AddTestResult($"禁言功能测试失败：{ex.Message}");
            ShowMessage("禁言功能测试失败：" + ex.Message, "danger");
        }
        
        AddTestResult("----------------------------------------");
        UpdateTestResults();
    }

    private void AddTestResult(string message)
    {
        testResults.AppendLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    }

    private void UpdateTestResults()
    {
        txtTestResults.Text = testResults.ToString();
    }

    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = "alert alert-" + type;
        pnlMessage.Visible = true;
    }
}
