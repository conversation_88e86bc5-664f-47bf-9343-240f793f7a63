<%@ Page Title="聊天系统" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ChatMain.aspx.cs" Inherits="Chat_ChatMain" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <style>
        .chat-container {
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .chat-sidebar {
            background-color: #f8f9fa;
            border-right: 1px solid #ddd;
            height: 100%;
            overflow-y: auto;
        }
        .chat-main {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: #fff;
        }
        .chat-input {
            border-top: 1px solid #ddd;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .message-item {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 70%;
        }
        .message-sent {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message-received {
            background-color: #e9ecef;
            color: #333;
        }
        .message-admin {
            border-left: 4px solid #dc3545;
            background-color: #fff5f5;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.1);
        }
        .message-admin.message-sent {
            background-color: #dc3545;
            border-left: 4px solid #fff;
        }
        .admin-badge {
            background-color: #dc3545;
            color: white;
            font-size: 0.75em;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 5px;
        }
        .admin-controls {
            margin-top: 5px;
            text-align: right;
        }
        .message-header {
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.8;
        }
        .message-content {
            font-size: 14px;
            line-height: 1.4;
        }
        .room-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .room-item:hover {
            background-color: #e9ecef;
        }
        .room-item.active {
            background-color: #007bff;
            color: white;
        }
        .room-name {
            font-weight: bold;
            margin-bottom: 3px;
        }
        .room-info {
            font-size: 12px;
            opacity: 0.8;
        }
        .unread-badge {
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            float: right;
        }
        .admin-controls {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
        .message-deleted {
            opacity: 0.5;
            font-style: italic;
        }
        .user-muted {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-comments"></i> 聊天系统</h2>
                <p class="text-muted">与其他用户交流沟通</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="row mb-3">
            <div class="col-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
            </div>
        </asp:Panel>

        <!-- 聊天界面 -->
        <div class="row">
            <div class="col-12">
                <div class="chat-container">
                    <div class="row h-100 no-gutters">
                        <!-- 聊天室列表 -->
                        <div class="col-md-3 chat-sidebar">
                            <div class="p-3 border-bottom">
                                <h5 class="mb-3"><i class="fas fa-list"></i> 聊天室</h5>
                                <asp:Button ID="btnCreatePrivateChat" runat="server" Text="创建私聊" 
                                    CssClass="btn btn-primary btn-sm btn-block mb-2" OnClick="btnCreatePrivateChat_Click" />
                                <asp:Button ID="btnRefreshRooms" runat="server" Text="刷新" 
                                    CssClass="btn btn-outline-secondary btn-sm btn-block" OnClick="btnRefreshRooms_Click" />
                            </div>
                            <div class="room-list">
                                <asp:Repeater ID="rptChatRooms" runat="server" OnItemCommand="rptChatRooms_ItemCommand">
                                    <ItemTemplate>
                                        <div class="room-item" data-room-id='<%# Eval("RoomID") %>'>
                                            <asp:LinkButton ID="lbtnSelectRoom" runat="server" 
                                                CommandName="SelectRoom" CommandArgument='<%# Eval("RoomID") %>'
                                                CssClass="text-decoration-none text-dark d-block">
                                                <div class="room-name">
                                                    <i class='<%# Eval("RoomType").ToString() == "Public" ? "fas fa-users" : "fas fa-user" %>'></i>
                                                    <%# Eval("RoomName") %>
                                                    <%# Convert.ToInt32(Eval("UnreadCount")) > 0 ? 
                                                        "<span class='unread-badge'>" + Eval("UnreadCount") + "</span>" : "" %>
                                                </div>
                                                <div class="room-info">
                                                    <%# Eval("RoomType").ToString() == "Public" ? "公共聊天" : "私人聊天" %>
                                                    <%# Eval("MessageCount") %> 条消息
                                                </div>
                                            </asp:LinkButton>
                                        </div>
                                    </ItemTemplate>
                                </asp:Repeater>
                            </div>
                        </div>

                        <!-- 聊天主界面 -->
                        <div class="col-md-9 chat-main">
                            <asp:Panel ID="pnlChatHeader" runat="server" CssClass="p-3 border-bottom bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0">
                                            <asp:Label ID="lblCurrentRoomName" runat="server" Text="请选择聊天室"></asp:Label>
                                            <asp:Label ID="lblCurrentRoomType" runat="server" CssClass="badge badge-secondary ml-2"></asp:Label>
                                        </h5>
                                    </div>
                                    <div>
                                        <asp:Panel ID="pnlAdminBadge" runat="server">
                                            <span class="badge badge-danger">
                                                <i class="fas fa-shield-alt"></i> 管理员
                                            </span>
                                        </asp:Panel>
                                    </div>
                                </div>
                            </asp:Panel>

                            <!-- 禁言提示 -->
                            <asp:Panel ID="pnlMuteNotice" runat="server" Visible="false" CssClass="user-muted">
                                <i class="fas fa-ban text-warning"></i>
                                <strong>您已被禁言</strong>
                                <asp:Label ID="lblMuteInfo" runat="server"></asp:Label>
                            </asp:Panel>

                            <!-- 消息显示区域 -->
                            <div class="chat-messages" id="chatMessages">
                                <asp:Panel ID="pnlNoRoomSelected" runat="server">
                                    <div class="text-center text-muted mt-5">
                                        <i class="fas fa-comments fa-3x mb-3"></i>
                                        <h5>欢迎使用聊天系统</h5>
                                        <p>请从左侧选择一个聊天室开始聊天</p>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlMessages" runat="server" Visible="false">
                                    <asp:Repeater ID="rptMessages" runat="server" OnItemCommand="rptMessages_ItemCommand">
                                        <ItemTemplate>
                                            <div class='<%# GetMessageCssClass(Eval("SenderID"), Eval("SenderType")) %>'>
                                                <div class="message-header">
                                                    <strong><%# Eval("SenderName") %></strong>
                                                    <small>(<%# GetUserTypeDisplayName(Eval("SenderType").ToString()) %>)</small>
                                                    <%# Eval("SenderType").ToString() == "Admin" ? "<span class='admin-badge'><i class='fas fa-shield-alt'></i> 管理员</span>" : "" %>
                                                    <span class="float-right"><%# Convert.ToDateTime(Eval("SentDate")).ToString("MM-dd HH:mm") %></span>
                                                </div>
                                                <div class="message-content">
                                                    <%# Eval("MessageContent") %>
                                                </div>
                                                <!-- 管理员删除按钮 -->
                                                <asp:Panel ID="pnlAdminControls" runat="server" Visible='<%# IsCurrentUserAdmin() %>' CssClass="admin-controls">
                                                    <asp:LinkButton ID="lbtnDeleteMessage" runat="server" 
                                                        CommandName="DeleteMessage" CommandArgument='<%# Eval("MessageID") %>'
                                                        CssClass="btn btn-sm btn-outline-danger" 
                                                        OnClientClick="return confirm('确定要删除这条消息吗？');">
                                                        <i class="fas fa-trash"></i> 删除
                                                    </asp:LinkButton>
                                                </asp:Panel>
                                            </div>
                                        </ItemTemplate>
                                    </asp:Repeater>
                                </asp:Panel>
                            </div>

                            <!-- 消息输入区域 -->
                            <asp:Panel ID="pnlChatInput" runat="server" Visible="false" CssClass="chat-input">
                                <!-- 管理员功能区 -->
                                <asp:Panel ID="pnlAdminFeatures" runat="server" CssClass="mb-2">
                                    <div class="btn-group" role="group">
                                        <asp:Button ID="btnSystemAnnouncement" runat="server" Text="系统公告"
                                            CssClass="btn btn-sm btn-warning" OnClick="btnSystemAnnouncement_Click" />
                                        <asp:Button ID="btnBroadcastMessage" runat="server" Text="全员广播"
                                            CssClass="btn btn-sm btn-info" OnClick="btnBroadcastMessage_Click" />
                                    </div>
                                </asp:Panel>

                                <div class="input-group">
                                    <asp:TextBox ID="txtMessage" runat="server" CssClass="form-control"
                                        placeholder="输入消息..." MaxLength="1000" onkeypress="return handleEnterKey(event);"></asp:TextBox>
                                    <div class="input-group-append">
                                        <asp:Button ID="btnSendMessage" runat="server" Text="发送"
                                            CssClass="btn btn-primary" OnClick="btnSendMessage_Click" />
                                    </div>
                                </div>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建私聊模态框 -->
        <div class="modal fade" id="createPrivateChatModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建私聊</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>搜索用户：</label>
                            <asp:TextBox ID="txtSearchUser" runat="server" CssClass="form-control" 
                                placeholder="输入用户名或邮箱搜索"></asp:TextBox>
                            <asp:Button ID="btnSearchUser" runat="server" Text="搜索" 
                                CssClass="btn btn-secondary btn-sm mt-2" OnClick="btnSearchUser_Click" />
                        </div>
                        <div class="form-group">
                            <label>选择用户：</label>
                            <asp:GridView ID="gvUsers" runat="server" AutoGenerateColumns="False" 
                                CssClass="table table-sm" OnRowCommand="gvUsers_RowCommand" EmptyDataText="没有找到用户">
                                <Columns>
                                    <asp:BoundField DataField="Username" HeaderText="用户名" />
                                    <asp:BoundField DataField="UserType" HeaderText="用户类型" />
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="lbtnCreateChat" runat="server" 
                                                CommandName="CreateChat" CommandArgument='<%# Eval("UserID") %>'
                                                CssClass="btn btn-sm btn-primary">
                                                创建聊天
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <asp:HiddenField ID="hfCurrentRoomID" runat="server" />
        <asp:HiddenField ID="hfAutoRefresh" runat="server" Value="true" />
    </div>

    <script type="text/javascript">
        function handleEnterKey(event) {
            if (event.keyCode === 13) {
                event.preventDefault();
                document.getElementById('<%= btnSendMessage.ClientID %>').click();
                return false;
            }
            return true;
        }

        // 自动滚动到最新消息
        function scrollToBottom() {
            var chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        // 页面加载完成后滚动到底部
        window.onload = function() {
            scrollToBottom();
        };

        // 定时刷新消息（可选）
        setInterval(function() {
            var autoRefresh = document.getElementById('<%= hfAutoRefresh.ClientID %>');
            var currentRoomID = document.getElementById('<%= hfCurrentRoomID.ClientID %>');
            if (autoRefresh && autoRefresh.value === 'true' && currentRoomID && currentRoomID.value !== '') {
                __doPostBack('<%= Page.UniqueID %>', 'RefreshMessages');
            }
        }, 10000); // 10秒刷新一次消息

        // 高亮当前选中的聊天室
        function highlightCurrentRoom() {
            var currentRoomID = document.getElementById('<%= hfCurrentRoomID.ClientID %>').value;
            if (currentRoomID) {
                $('.room-item').removeClass('active');
                $('.room-item[data-room-id="' + currentRoomID + '"]').addClass('active');
            }
        }

        $(document).ready(function() {
            highlightCurrentRoom();
        });
    </script>
</asp:Content>
