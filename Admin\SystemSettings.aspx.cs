using System;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Web.UI;
using System.Net.Mail;
using System.Net;
using System.Configuration;

public partial class Admin_SystemSettings : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 加载系统设置
            LoadSystemSettings();
        }
    }

    /// <summary>
    /// 加载系统设置
    /// </summary>
    private void LoadSystemSettings()
    {
        try
        {
            // 确保SystemSettings表存在
            EnsureSystemSettingsTableExists();
            
            // 加载邮件设置
            string smtpServer = GetSetting("SmtpServer");
            string smtpPort = GetSetting("SmtpPort");
            string emailAddress = GetSetting("EmailAddress");
            string emailPassword = GetSetting("EmailPassword");
            string enableSsl = GetSetting("EnableSsl");

            txtSmtpServer.Text = smtpServer;
            txtSmtpPort.Text = smtpPort;
            txtEmailAddress.Text = emailAddress;
            txtEmailPassword.Text = emailPassword;
            chkEnableSsl.Checked = enableSsl == "true";

            // 加载安全设置
            string minPasswordLength = GetSetting("MinPasswordLength");
            string requireUppercase = GetSetting("RequireUppercase");
            string requireDigit = GetSetting("RequireDigit");
            string requireSpecialChar = GetSetting("RequireSpecialChar");
            string maxLoginAttempts = GetSetting("MaxLoginAttempts");
            string lockoutDuration = GetSetting("LockoutDuration");

            txtMinPasswordLength.Text = minPasswordLength;
            chkRequireUppercase.Checked = requireUppercase == "true";
            chkRequireDigit.Checked = requireDigit == "true";
            chkRequireSpecialChar.Checked = requireSpecialChar == "true";
            txtMaxLoginAttempts.Text = maxLoginAttempts;
            txtLockoutDuration.Text = lockoutDuration;
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载系统设置时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 确保SystemSettings表存在
    /// </summary>
    private void EnsureSystemSettingsTableExists()
    {
        try
        {
            string checkTableQuery = @"
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SystemSettings')
                BEGIN
                    CREATE TABLE SystemSettings (
                        SettingID INT PRIMARY KEY IDENTITY(1,1),
                        SettingKey NVARCHAR(100) NOT NULL UNIQUE,
                        SettingValue NVARCHAR(MAX),
                        Description NVARCHAR(500),
                        UpdatedDate DATETIME DEFAULT GETDATE()
                    )
                END";

            DatabaseHelper.ExecuteNonQuery(checkTableQuery);
            
            // 如果表是新创建的，添加默认设置
            InitializeDefaultSettings();
        }
        catch (Exception ex)
        {
            throw new Exception("创建系统设置表失败：" + ex.Message);
        }
    }
    
    /// <summary>
    /// 初始化默认系统设置
    /// </summary>
    private void InitializeDefaultSettings()
    {
        // 检查是否已有设置，如果没有则添加默认设置
        string countQuery = "SELECT COUNT(*) FROM SystemSettings";
        int count = Convert.ToInt32(DatabaseHelper.ExecuteScalar(countQuery));
        
        if (count == 0)
        {
            // 邮件设置默认值
            SaveSetting("SmtpServer", "smtp.example.com");
            SaveSetting("SmtpPort", "587");
            SaveSetting("EmailAddress", "<EMAIL>");
            SaveSetting("EmailPassword", "");
            SaveSetting("EnableSsl", "true");
            
            // 安全设置默认值
            SaveSetting("MinPasswordLength", "8");
            SaveSetting("RequireUppercase", "true");
            SaveSetting("RequireDigit", "true");
            SaveSetting("RequireSpecialChar", "false");
            SaveSetting("MaxLoginAttempts", "5");
            SaveSetting("LockoutDuration", "30");
        }
    }

    /// <summary>
    /// 获取系统设置
    /// </summary>
    private string GetSetting(string key)
    {
        string query = "SELECT SettingValue FROM SystemSettings WHERE SettingKey = @SettingKey";
        SqlParameter parameter = new SqlParameter("@SettingKey", key);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        return result != null && result != DBNull.Value ? result.ToString() : string.Empty;
    }

    /// <summary>
    /// 保存系统设置
    /// </summary>
    private void SaveSetting(string key, string value)
    {
        string query = @"IF EXISTS (SELECT 1 FROM SystemSettings WHERE SettingKey = @SettingKey)
                        UPDATE SystemSettings SET SettingValue = @SettingValue WHERE SettingKey = @SettingKey
                        ELSE
                        INSERT INTO SystemSettings (SettingKey, SettingValue) VALUES (@SettingKey, @SettingValue)";

        SqlParameter[] parameters =
        {
            new SqlParameter("@SettingKey", key),
            new SqlParameter("@SettingValue", value)
        };

        DatabaseHelper.ExecuteNonQuery(query, parameters);
    }

    /// <summary>
    /// 备份数据库按钮点击事件
    /// </summary>
    protected void btnBackupDatabase_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取数据库连接字符串
            string connectionString = ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString;
            SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
            string databaseName = builder.InitialCatalog;

            // 创建备份文件名
            string backupFolder = Server.MapPath("~/App_Data/Backups");
            if (!Directory.Exists(backupFolder))
            {
                Directory.CreateDirectory(backupFolder);
            }

            string backupFileName = string.Format("{0}_{1:yyyyMMdd_HHmmss}.bak", databaseName, DateTime.Now);
            string backupFilePath = System.IO.Path.Combine(backupFolder, backupFileName);

            // 执行数据库备份
            string backupQuery = string.Format("BACKUP DATABASE [{0}] TO DISK = '{1}' WITH FORMAT, MEDIANAME = '{0}', NAME = '{0} Backup'", databaseName, backupFilePath);
            DatabaseHelper.ExecuteNonQuery(backupQuery);

            lblBackupMessage.Text = "数据库备份成功！备份文件：" + backupFileName;
            lblBackupMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblBackupMessage.Text = "数据库备份失败：" + ex.Message;
            lblBackupMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 保存邮件设置按钮点击事件
    /// </summary>
    protected void btnSaveEmailSettings_Click(object sender, EventArgs e)
    {
        try
        {
            // 保存邮件设置
            SaveSetting("SmtpServer", txtSmtpServer.Text.Trim());
            SaveSetting("SmtpPort", txtSmtpPort.Text.Trim());
            SaveSetting("EmailAddress", txtEmailAddress.Text.Trim());
            SaveSetting("EmailPassword", txtEmailPassword.Text);
            SaveSetting("EnableSsl", chkEnableSsl.Checked.ToString().ToLower());

            lblMessage.Text = "邮件设置保存成功";
            lblMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblMessage.Text = "保存邮件设置时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 测试邮件设置按钮点击事件
    /// </summary>
    protected void btnTestEmail_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取邮件设置
            string smtpServer = txtSmtpServer.Text.Trim();
            int smtpPort = Convert.ToInt32(txtSmtpPort.Text.Trim());
            string emailAddress = txtEmailAddress.Text.Trim();
            string emailPassword = txtEmailPassword.Text;
            bool enableSsl = chkEnableSsl.Checked;

            // 创建邮件客户端
            SmtpClient client = new SmtpClient(smtpServer, smtpPort);
            client.EnableSsl = enableSsl;
            client.Credentials = new NetworkCredential(emailAddress, emailPassword);
            client.DeliveryMethod = SmtpDeliveryMethod.Network;

            // 创建测试邮件
            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(emailAddress, "汽车维修服务平台");
            mail.To.Add(emailAddress);
            mail.Subject = "汽车维修服务平台 - 邮件设置测试";
            mail.Body = "这是一封测试邮件，用于验证汽车维修服务平台的邮件设置是否正确。\n\n发送时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            mail.IsBodyHtml = false;

            // 发送测试邮件
            client.Send(mail);

            lblMessage.Text = "测试邮件发送成功，请检查您的邮箱";
            lblMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblMessage.Text = "测试邮件发送失败：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 保存安全设置按钮点击事件
    /// </summary>
    protected void btnSaveSecuritySettings_Click(object sender, EventArgs e)
    {
        try
        {
            // 保存安全设置
            SaveSetting("MinPasswordLength", txtMinPasswordLength.Text.Trim());
            SaveSetting("RequireUppercase", chkRequireUppercase.Checked.ToString().ToLower());
            SaveSetting("RequireDigit", chkRequireDigit.Checked.ToString().ToLower());
            SaveSetting("RequireSpecialChar", chkRequireSpecialChar.Checked.ToString().ToLower());
            SaveSetting("MaxLoginAttempts", txtMaxLoginAttempts.Text.Trim());
            SaveSetting("LockoutDuration", txtLockoutDuration.Text.Trim());

            lblMessage.Text = "安全设置保存成功";
            lblMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblMessage.Text = "保存安全设置时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 清理系统按钮点击事件
    /// </summary>
    protected void btnCleanupSystem_Click(object sender, EventArgs e)
    {
        try
        {
            // 清理临时文件
            string tempFolder = Server.MapPath("~/App_Data/Temp");
            if (Directory.Exists(tempFolder))
            {
                DirectoryInfo di = new DirectoryInfo(tempFolder);
                foreach (FileInfo file in di.GetFiles())
                {
                    try
                    {
                        file.Delete();
                    }
                    catch { }
                }
            }

            // 清理日志文件（保留最近7天的日志）
            string logFolder = Server.MapPath("~/App_Data/Logs");
            if (Directory.Exists(logFolder))
            {
                DirectoryInfo di = new DirectoryInfo(logFolder);
                foreach (FileInfo file in di.GetFiles("*.log"))
                {
                    try
                    {
                        if (file.CreationTime < DateTime.Now.AddDays(-7))
                        {
                            file.Delete();
                        }
                    }
                    catch { }
                }
            }

            lblMaintenanceMessage.Text = "系统清理成功";
            lblMaintenanceMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblMaintenanceMessage.Text = "系统清理失败：" + ex.Message;
            lblMaintenanceMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 优化数据库按钮点击事件
    /// </summary>
    protected void btnOptimizeDatabase_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取数据库连接字符串

            string connectionString = ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString;
            SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
            string databaseName = builder.InitialCatalog;

            // 执行数据库优化
            string optimizeQuery = string.Format(@"
                -- 重建索引
                EXEC sp_MSforeachtable @command1=""PRINT 'Rebuilding indexes for: ?';"", @command2=""ALTER INDEX ALL ON ? REBUILD WITH (FILLFACTOR = 80, ONLINE = OFF);"";
                
                -- 更新统计信息
                EXEC sp_updatestats;
                
                -- 收缩数据库
                DBCC SHRINKDATABASE (N'{0}', 10);", databaseName);

            DatabaseHelper.ExecuteNonQuery(optimizeQuery);

            lblMaintenanceMessage.Text = "数据库优化成功";
            lblMaintenanceMessage.CssClass = "text-success";
        }
        catch (Exception ex)
        {
            lblMaintenanceMessage.Text = "数据库优化失败：" + ex.Message;
            lblMaintenanceMessage.CssClass = "text-danger";
        }
    }
} 