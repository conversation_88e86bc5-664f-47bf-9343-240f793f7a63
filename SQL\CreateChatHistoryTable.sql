-- 创建聊天历史表
USE CarRepairServiceDB;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ChatHistory')
BEGIN
    CREATE TABLE ChatHistory (
        ChatID INT PRIMARY KEY IDENTITY(1,1),
        UserID INT NOT NULL,
        UserMessage NVARCHAR(MAX) NOT NULL,
        AssistantResponse NVARCHAR(MAX) NOT NULL,
        Intent NVARCHAR(50),
        ChatDate DATETIME DEFAULT GETDATE(),
        SessionID NVARCHAR(100),
        UserLocation NVARCHAR(100),
        ResponseTime INT, -- 响应时间（毫秒）
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    );
    
    -- 创建索引
    CREATE INDEX IX_ChatHistory_UserID ON ChatHistory(UserID);
    CREATE INDEX IX_ChatHistory_ChatDate ON ChatHistory(ChatDate);
    CREATE INDEX IX_ChatHistory_Intent ON ChatHistory(Intent);
    
    PRINT '聊天历史表 ChatHistory 创建成功';
END
ELSE
BEGIN
    PRINT '聊天历史表 ChatHistory 已存在';
END

-- 确认表已创建
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME = 'ChatHistory';
