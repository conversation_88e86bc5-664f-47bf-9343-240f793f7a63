//------------------------------------------------------------------------------
// <自动生成>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，如果
//     重新生成代码，则所做更改将丢失。
// </自动生成>
//------------------------------------------------------------------------------

public partial class Chat_ChatMain
{
    /// <summary>
    /// pnlMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlMessage;

    /// <summary>
    /// lblMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblMessage;

    /// <summary>
    /// btnCreatePrivateChat 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnCreatePrivateChat;

    /// <summary>
    /// btnRefreshRooms 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnRefreshRooms;

    /// <summary>
    /// rptChatRooms 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Repeater rptChatRooms;

    /// <summary>
    /// pnlChatHeader 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlChatHeader;

    /// <summary>
    /// lblCurrentRoomName 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblCurrentRoomName;

    /// <summary>
    /// lblCurrentRoomType 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblCurrentRoomType;

    /// <summary>
    /// pnlAdminBadge 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlAdminBadge;

    /// <summary>
    /// pnlMuteNotice 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlMuteNotice;

    /// <summary>
    /// lblMuteInfo 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblMuteInfo;

    /// <summary>
    /// pnlNoRoomSelected 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlNoRoomSelected;

    /// <summary>
    /// pnlMessages 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlMessages;

    /// <summary>
    /// rptMessages 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Repeater rptMessages;

    /// <summary>
    /// pnlChatInput 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlChatInput;

    /// <summary>
    /// pnlAdminFeatures 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlAdminFeatures;

    /// <summary>
    /// txtMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtMessage;

    /// <summary>
    /// btnSystemAnnouncement 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSystemAnnouncement;

    /// <summary>
    /// btnBroadcastMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnBroadcastMessage;

    /// <summary>
    /// btnSendMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSendMessage;

    /// <summary>
    /// txtSearchUser 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtSearchUser;

    /// <summary>
    /// btnSearchUser 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSearchUser;

    /// <summary>
    /// gvUsers 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.GridView gvUsers;

    /// <summary>
    /// hfCurrentRoomID 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField hfCurrentRoomID;

    /// <summary>
    /// hfAutoRefresh 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField hfAutoRefresh;
}
