-- 创建零件表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Parts' AND xtype='U')
BEGIN
    CREATE TABLE Parts (
        PartID int IDENTITY(1,1) PRIMARY KEY,
        ShopID int NOT NULL,
        PartName nvarchar(100) NOT NULL,
        PartNumber nvarchar(50),
        Price decimal(10,2) NOT NULL,
        StockQuantity int NOT NULL DEFAULT 0,
        Description nvarchar(500),
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        ModifiedDate datetime,
        FOREIGN KEY (ShopID) REFERENCES RepairShops(ShopID)
    );
    
    CREATE INDEX IX_Parts_ShopID ON Parts(ShopID);
    CREATE INDEX IX_Parts_PartNumber ON Parts(PartNumber);
    
    PRINT '零件表 Parts 创建成功';
END
ELSE
BEGIN
    PRINT '零件表 Parts 已存在';
END

-- 创建维修记录零件使用表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ServiceRecordParts' AND xtype='U')
BEGIN
    CREATE TABLE ServiceRecordParts (
        RecordPartID int IDENTITY(1,1) PRIMARY KEY,
        RecordID int NOT NULL,
        PartID int NOT NULL,
        Quantity int NOT NULL DEFAULT 1,
        UnitPrice decimal(10,2) NOT NULL,
        TotalPrice AS (Quantity * UnitPrice),
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (RecordID) REFERENCES ServiceRecords(RecordID),
        FOREIGN KEY (PartID) REFERENCES Parts(PartID)
    );
    
    CREATE INDEX IX_ServiceRecordParts_RecordID ON ServiceRecordParts(RecordID);
    CREATE INDEX IX_ServiceRecordParts_PartID ON ServiceRecordParts(PartID);
    
    PRINT '维修记录零件使用表 ServiceRecordParts 创建成功';
END
ELSE
BEGIN
    PRINT '维修记录零件使用表 ServiceRecordParts 已存在';
END

-- 插入一些示例零件数据（可选）
IF EXISTS (SELECT * FROM RepairShops WHERE ShopID = 1)
BEGIN
    IF NOT EXISTS (SELECT * FROM Parts WHERE ShopID = 1)
    BEGIN
        INSERT INTO Parts (ShopID, PartName, PartNumber, Price, StockQuantity, Description) VALUES
        (1, '机油滤清器', 'OF001', 25.00, 50, '适用于大部分车型的机油滤清器'),
        (1, '空气滤清器', 'AF001', 35.00, 30, '高效空气过滤器'),
        (1, '火花塞', 'SP001', 15.00, 100, '标准火花塞'),
        (1, '刹车片', 'BP001', 120.00, 20, '前轮刹车片'),
        (1, '雨刷器', 'WB001', 45.00, 25, '通用雨刷器'),
        (1, '轮胎', 'TR001', 350.00, 10, '205/55R16 轮胎'),
        (1, '电瓶', 'BT001', 280.00, 8, '12V 60Ah 汽车电瓶'),
        (1, '变速箱油', 'TO001', 85.00, 15, '自动变速箱专用油');
        
        PRINT '示例零件数据插入成功';
    END
    ELSE
    BEGIN
        PRINT '零件数据已存在，跳过插入';
    END
END
ELSE
BEGIN
    PRINT '未找到维修店数据，跳过示例零件数据插入';
END