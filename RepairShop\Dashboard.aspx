<%@ Page Title="维修店控制台" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="RepairShop_Dashboard" Codebehind="Dashboard.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <!-- 引入高德地图API，使用新的API密钥和私钥 -->
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: '78467c9c5852e1e36eeae0c8d21c0bf9',
        }
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=95a8d6c364456acd37faf8d0d8cfbc04&plugin=AMap.Geocoder,AMap.ToolBar,AMap.Scale,AMap.Geolocation,AMap.CitySearch"></script>
    <style>
        #mapContainer {
            width: 100%;
            height: 300px;
            margin-top: 10px;
            display: none;
        }
        .map-search-box {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 999;
            width: 280px;
        }
        .map-search-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .map-marker-info {
            background-color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            max-width: 200px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt"></i> 维修店控制台</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-clock"></i> 待处理预约</h5>
                                <h2><asp:Label ID="lblPendingAppointments" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-calendar-check fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-primary">
                        <a class="small text-white stretched-link" href="Appointments.aspx">查看详情</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-check-circle"></i> 已确认预约</h5>
                                <h2><asp:Label ID="lblConfirmedAppointments" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-clipboard-check fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-success">
                        <a class="small text-white stretched-link" href="Appointments.aspx">查看详情</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-tools"></i> 服务项目</h5>
                                <h2><asp:Label ID="lblTotalServices" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-wrench fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-info">
                        <a class="small text-white stretched-link" href="Services.aspx">管理服务</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-users"></i> 员工管理</h5>
                                <h2><asp:Label ID="lblTotalEmployees" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-user-friends fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-warning">
                        <a class="small text-white stretched-link" href="EmployeeManagement.aspx">管理员工</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-bolt"></i> 快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="EmployeeManagement.aspx" class="btn btn-warning btn-block mb-2">
                                    <i class="fas fa-users"></i> 员工管理
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="Appointments.aspx" class="btn btn-primary btn-block mb-2">
                                    <i class="fas fa-calendar-check"></i> 预约管理
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="ServiceRecords.aspx" class="btn btn-info btn-block mb-2">
                                    <i class="fas fa-wrench"></i> 维修记录
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="Services.aspx" class="btn btn-success btn-block mb-2">
                                    <i class="fas fa-tools"></i> 服务管理
                                </a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <a href="../Chat/ChatMain.aspx" class="btn btn-purple btn-block mb-2" style="background-color: #6f42c1; color: white;">
                                    <i class="fas fa-comments"></i> 聊天交流
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-store"></i> 店铺信息</h5>
                            <asp:LinkButton ID="lbtnEditShop" runat="server" CssClass="btn btn-sm btn-light" OnClick="lbtnEditShop_Click">
                                <i class="fas fa-edit"></i> 编辑信息
                            </asp:LinkButton>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlShopInfo" runat="server">
                            <dl class="row">
                                <dt class="col-sm-4">店铺照片：</dt>
                                <dd class="col-sm-8">
                                    <asp:Image ID="imgShopPhoto" runat="server" Width="120px" Height="80px" />
                                </dd>

                                <dt class="col-sm-4">店铺名称：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">营业地址：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblAddress" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">营业时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblBusinessHours" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">联系人：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblContactPerson" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">店铺评分：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblRating" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </asp:Panel>

                        <asp:Panel ID="pnlEditShop" runat="server" Visible="false">
                            <div class="form-group row">
                                <label for="fuShopPhoto" class="col-sm-4 col-form-label">店铺照片：</label>
                                <div class="col-sm-8">
                                    <asp:FileUpload ID="fuShopPhoto" runat="server" CssClass="form-control-file" />
                                    <asp:Image ID="imgShopPhotoPreview" runat="server" Width="120px" Height="80px" Style="margin-top:8px;" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtShopName" class="col-sm-4 col-form-label">店铺名称：</label>
                                <div class="col-sm-8">
                                    <asp:TextBox ID="txtShopName" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvShopName" runat="server" ControlToValidate="txtShopName"
                                        ErrorMessage="店铺名称不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="ShopInfo"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtDescription" class="col-sm-4 col-form-label">店铺描述：</label>
                                <div class="col-sm-8">
                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2"></asp:TextBox>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtAddress" class="col-sm-4 col-form-label">营业地址：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control"></asp:TextBox>
                                        <div class="input-group-append">
                                            <button type="button" id="btnShowMap" class="btn btn-outline-secondary" onclick="toggleMap(); return false;">
                                                <i class="fas fa-map-marker-alt"></i> 地图选点
                                            </button>
                                            <button type="button" id="btnCurrentLocation" class="btn btn-outline-primary" onclick="getCurrentLocation()">
                                                <i class="fa fa-location-arrow"></i> 定位当前位置
                                            </button>
                                        </div>
                                    </div>
                                    <asp:RequiredFieldValidator ID="rfvAddress" runat="server" ControlToValidate="txtAddress"
                                        ErrorMessage="营业地址不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="ShopInfo"></asp:RequiredFieldValidator>
                                    
                                    <!-- 地图容器 -->
                                    <div id="mapContainer"></div>
                                    
                                    <!-- 添加地图搜索框 -->
                                    <div id="mapSearchBox" style="display:none; margin:10px 0;">
                                        <div class="input-group">
                                            <input type="text" id="searchMapInput" class="form-control" placeholder="输入地址搜索">
                                            <div class="input-group-append">
                                                <button id="searchMapBtn" class="btn btn-outline-secondary" type="button">
                                                    <i class="fas fa-search"></i> 搜索
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 隐藏字段存储经纬度 -->
                                    <asp:HiddenField ID="hfLongitude" runat="server" />
                                    <asp:HiddenField ID="hfLatitude" runat="server" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtBusinessHours" class="col-sm-4 col-form-label">营业时间：</label>
                                <div class="col-sm-8">
                                    <asp:TextBox ID="txtBusinessHours" runat="server" CssClass="form-control" placeholder="例如：周一至周五 9:00-18:00"></asp:TextBox>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtContactPerson" class="col-sm-4 col-form-label">联系人：</label>
                                <div class="col-sm-8">
                                    <asp:TextBox ID="txtContactPerson" runat="server" CssClass="form-control"></asp:TextBox>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-8 offset-sm-4">
                                    <asp:Button ID="btnSaveShopInfo" runat="server" Text="保存" CssClass="btn btn-primary" ValidationGroup="ShopInfo" OnClick="btnSaveShopInfo_Click" />
                                    <asp:Button ID="btnCancelEdit" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancelEdit_Click" CausesValidation="false" />
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> 店铺统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">已完成订单</div>
                                <div class="h3"><asp:Label ID="lblCompletedOrders" runat="server" Text="0"></asp:Label></div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">总评价数</div>
                                <div class="h3"><asp:Label ID="lblTotalReviews" runat="server" Text="0"></asp:Label></div>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">总收入</div>
                                <div class="h3 text-success"><asp:Label ID="lblTotalRevenue" runat="server" Text="¥0.00"></asp:Label></div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">平均评分</div>
                                <h3>
                                    <asp:Label ID="lblAverageRating" runat="server" CssClass="text-warning"></asp:Label>
                                    <small>/5</small>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-bullhorn"></i> 系统通知</h5>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptNotifications" runat="server">
                            <ItemTemplate>
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> <%# Eval("Title") %></h6>
                                        <small class="text-muted"><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("yyyy-MM-dd") %></small>
                                    </div>
                                    <p class="mb-0"><%# Eval("Content") %></p>
                                </div>
                            </ItemTemplate>
                            <FooterTemplate>
                                <% if (rptNotifications.Items.Count == 0) { %>
                                    <div class="alert alert-light">
                                        暂无系统通知
                                    </div>
                                <% } %>
                            </FooterTemplate>
                        </asp:Repeater>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> 待处理预约</h5>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvPendingAppointments" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" DataKeyNames="AppointmentID"
                            OnRowCommand="gvPendingAppointments_RowCommand" EmptyDataText="暂无待处理预约">
                            <Columns>
                                <asp:BoundField DataField="AppointmentDate" HeaderText="预约时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆信息" />
                                <asp:BoundField DataField="OwnerName" HeaderText="车主" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnConfirm" runat="server" CssClass="btn btn-sm btn-success"
                                            CommandName="ConfirmAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="确认预约">
                                            <i class="fas fa-check"></i> 确认
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info ml-1"
                                            CommandName="ViewAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="查看详情">
                                            <i class="fas fa-eye"></i> 查看
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                        <div class="text-right mt-2">
                            <a href="Appointments.aspx" class="btn btn-outline-primary">查看所有预约 <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
    
    <!-- 高德地图JavaScript -->
    <script type="text/javascript">
        var map = null;
        var marker = null;
        var geocoder = null;
        var isMapInitialized = false;

        // 初始化地图
        function initMap() {
            if (isMapInitialized) return;
            
            console.log("开始初始化地图...");
            
            try {
                // 创建地图实例
                map = new AMap.Map('mapContainer', {
                    zoom: 13,
                    resizeEnable: true
                });
                
                console.log("地图创建成功");
                
                // 添加控件
                map.plugin(['AMap.ToolBar', 'AMap.Scale'], function () {
                    map.addControl(new AMap.ToolBar());
                    map.addControl(new AMap.Scale());
                    console.log("地图控件添加成功");
                });
                
                // 创建地址解析器实例
                try {
                    geocoder = new AMap.Geocoder({
                        city: "全国", // 城市，默认："全国"
                        radius: 1000 // 范围，默认：500
                    });
                    console.log("地理编码器创建成功");
                } catch (geoErr) {
                    console.error("创建地理编码器失败:", geoErr);
                    alert("地图地址解析组件加载失败，可能影响地址搜索功能");
                }
                
                // 设置标记
                marker = new AMap.Marker({
                    position: map.getCenter(),
                    draggable: true,
                    cursor: 'move'
                });
                
                // 将标记添加到地图
                marker.setMap(map);
                console.log("地图标记创建成功");
                
                // 添加标记拖拽结束事件
                marker.on('dragend', function(e) {
                    try {
                        var position = e.target.getPosition();
                        console.log("标记拖拽结束，位置:", position.getLng(), position.getLat());
                        updateAddressFromLocation(position);
                        document.getElementById('<%= hfLongitude.ClientID %>').value = position.getLng();
                        document.getElementById('<%= hfLatitude.ClientID %>').value = position.getLat();
                    } catch (err) {
                        console.error("处理标记拖拽事件出错:", err);
                    }
                });
                
                // 添加地图点击事件
                map.on('click', function(e) {
                    try {
                        console.log("地图点击，位置:", e.lnglat.getLng(), e.lnglat.getLat());
                        marker.setPosition(e.lnglat);
                        updateAddressFromLocation(e.lnglat);
                        document.getElementById('<%= hfLongitude.ClientID %>').value = e.lnglat.getLng();
                        document.getElementById('<%= hfLatitude.ClientID %>').value = e.lnglat.getLat();
                    } catch (err) {
                        console.error("处理地图点击事件出错:", err);
                    }
                });
                
                isMapInitialized = true;
                console.log("地图初始化完成");
            } catch (err) {
                console.error("地图初始化失败:", err);
                alert("地图初始化失败: " + err.message);
            }
        }
        
        // 显示/隐藏地图
        function toggleMap() {
            var mapContainer = document.getElementById('mapContainer');
            var searchBox = document.getElementById('mapSearchBox');
            
            if (mapContainer.style.display === 'none' || mapContainer.style.display === '') {
                mapContainer.style.display = 'block';
                searchBox.style.display = 'block';
                initMap();
                
                // 如果已经有经纬度，则定位到该位置
                var longitude = document.getElementById('<%= hfLongitude.ClientID %>').value;
                var latitude = document.getElementById('<%= hfLatitude.ClientID %>').value;
                
                if (longitude && latitude) {
                    var position = new AMap.LngLat(longitude, latitude);
                    map.setCenter(position);
                    marker.setPosition(position);
                }
                
                // 确保搜索按钮事件绑定
                initSearchControls();
            } else {
                mapContainer.style.display = 'none';
                searchBox.style.display = 'none';
            }
        }
        
        // 初始化搜索控件
        function initSearchControls() {
            console.log("初始化搜索控件...");
            try {
                // 获取搜索框和按钮元素
                var searchInput = document.getElementById('searchMapInput');
                var searchBtn = document.getElementById('searchMapBtn');
                
                if (!searchInput || !searchBtn) {
                    console.error("搜索框或搜索按钮元素不存在!");
                return;
            }
            
                console.log("获取到搜索框和按钮元素");
                
                // 确保事件只绑定一次，先移除可能已存在的事件
                searchBtn.removeEventListener('click', handleSearchBtnClick);
                searchInput.removeEventListener('keypress', handleSearchInputKeypress);
                
                // 绑定点击事件
                searchBtn.addEventListener('click', handleSearchBtnClick);
                console.log("搜索按钮点击事件已绑定");
                
                // 绑定回车键事件
                searchInput.addEventListener('keypress', handleSearchInputKeypress);
                console.log("搜索框回车键事件已绑定");
                
                // 初始化自动完成功能
                AMap.plugin('AMap.AutoComplete', function() {
                    var autoComplete = new AMap.AutoComplete({
                        input: 'searchMapInput'
                    });
                    console.log("地址自动完成功能已初始化");
                });
                
                console.log("搜索控件初始化完成");
                } catch (err) {
                console.error("初始化搜索控件时出错:", err);
            }
        }
        
        // 处理搜索按钮点击
        function handleSearchBtnClick(event) {
            console.log("搜索按钮被点击");
            event.preventDefault();
            doAddressSearch();
        }
        
        // 处理搜索框回车键
        function handleSearchInputKeypress(event) {
            if (event.key === 'Enter') {
                console.log("搜索框回车键被按下");
                event.preventDefault();
                doAddressSearch();
            }
        }
        
        // 执行地址搜索
        function doAddressSearch() {
            console.log("执行地址搜索...");
            var address = document.getElementById('searchMapInput').value;
            
            if (!address) {
                alert("请输入要搜索的地址");
                return;
            }
            
            console.log("搜索地址:", address);
            
            // 显示加载指示器
            var loadingTip = document.createElement('div');
            loadingTip.id = 'searchLoadingTip';
            loadingTip.className = 'alert alert-info';
            loadingTip.style.position = 'fixed';
            loadingTip.style.top = '10px';
            loadingTip.style.left = '50%';
            loadingTip.style.transform = 'translateX(-50%)';
            loadingTip.style.padding = '10px 20px';
            loadingTip.style.zIndex = '99999';
            loadingTip.innerHTML = '正在搜索地址...';
            document.body.appendChild(loadingTip);
            
            // 确保地图已初始化
            if (!isMapInitialized || !map) {
                console.log("地图未初始化，正在初始化地图...");
                initMap();
                
                // 等待地图初始化完成再搜索
                setTimeout(function() {
                    performGeocoderSearch(address);
                }, 1000);
            } else {
                // 地图已初始化，直接搜索
                performGeocoderSearch(address);
            }
        }
        
        // 使用地理编码器搜索地址
        function performGeocoderSearch(address) {
            console.log("开始地理编码搜索:", address);
            
            // 加载地理编码插件
            AMap.plugin(['AMap.Geocoder'], function() {
                try {
                    console.log("AMap.Geocoder插件加载成功");
                    
                    // 创建地理编码实例
                    var geocoder = new AMap.Geocoder({
                        city: "全国",      // 城市范围，默认："全国"
                        radius: 1000,      // 查找范围，默认：500
                        extensions: 'all'  // 返回完整地址信息
                    });
                    
                    console.log("AMap.Geocoder实例创建成功，开始搜索...");
                    
                    // 设置搜索超时
                    var searchTimeout = setTimeout(function() {
                        console.warn("地址搜索超时");
                        removeLoadingTip();
                        alert("地址搜索请求超时，请重试");
                    }, 10000);
                    
                    // 执行搜索
                geocoder.getLocation(address, function(status, result) {
                        // 清除超时定时器
                    clearTimeout(searchTimeout);
                    
                        // 移除加载提示
                        removeLoadingTip();
                        
                        console.log("地理编码搜索返回状态:", status);
                        console.log("地理编码搜索返回结果:", result);
                        
                        if (status === 'complete' && result.info === 'OK') {
                            // 检查搜索结果
                            if (result.geocodes && result.geocodes.length > 0) {
                                // 获取第一个结果
                                var geocode = result.geocodes[0];
                                var location = geocode.location;
                                var formattedAddress = geocode.formattedAddress;
                                
                                console.log("找到地址:", formattedAddress);
                                console.log("地址坐标:", location);
                                
                                // 确保位置有效
                                if (location && ((typeof location.getLng === 'function') || (typeof location.lng !== 'undefined'))) {
                                    // 获取经纬度
                                    var lng = typeof location.getLng === 'function' ? location.getLng() : location.lng;
                                    var lat = typeof location.getLat === 'function' ? location.getLat() : location.lat;
                                    
                                    // 验证经纬度
                                    if (lng && lat) {
                                        // 创建位置对象
                                        var position = new AMap.LngLat(lng, lat);
                                        
                                        // 更新地图视图
                            map.setCenter(position);
                                        map.setZoom(14); // 设置适当的缩放级别
                                        
                                        // 移动标记
                            marker.setPosition(position);
                                        
                                        // 更新隐藏字段
                                        document.getElementById('<%= hfLongitude.ClientID %>').value = lng;
                                        document.getElementById('<%= hfLatitude.ClientID %>').value = lat;
                                        
                                        // 更新地址字段
                        document.getElementById('<%= txtAddress.ClientID %>').value = formattedAddress;
                        
                                        // 显示成功提示
                                        showNotification('success', '地址搜索成功!');
                                        
                                        return; // 成功完成
                                    }
                                }
                                
                                console.error("搜索结果中无有效坐标");
                                showNotification('warning', '搜索结果中无有效坐标');
                    } else {
                                console.warn("搜索结果为空");
                                showNotification('warning', '未找到匹配的地址，请尝试更精确的搜索');
                            }
                        } else {
                            // 处理错误情况
                            console.error("地址搜索失败:", status, result ? result.info : "未知错误");
                            
                            var errorMsg = "地址搜索失败";
                            if (result) {
                                if (result.info === 'INVALID_REQUEST') {
                                    errorMsg = '无效的搜索请求，请输入更详细的地址';
                                } else if (result.info === 'INVALID_USER_KEY') {
                                    errorMsg = 'API密钥无效';
                                } else if (result.info === 'SERVICE_NOT_AVAILABLE') {
                                    errorMsg = '服务暂时不可用，请稍后再试';
                        } else {
                                    errorMsg += ': ' + result.info;
                        }
                    }
                            
                            showNotification('error', errorMsg);
                        }
                    });
            } catch (err) {
                    console.error("执行地理编码搜索时出错:", err);
                    removeLoadingTip();
                    showNotification('error', '搜索过程中出错: ' + err.message);
                }
            });
        }
        
        // 显示通知
        function showNotification(type, message) {
            var className = 'alert-info';
            if (type === 'success') className = 'alert-success';
            if (type === 'warning') className = 'alert-warning';
            if (type === 'error') className = 'alert-danger';
            
            var notification = document.createElement('div');
            notification.className = 'alert ' + className;
            notification.style.position = 'fixed';
            notification.style.top = '10px';
            notification.style.left = '50%';
            notification.style.transform = 'translateX(-50%)';
            notification.style.padding = '10px 20px';
            notification.style.zIndex = '99999';
            notification.innerHTML = message;
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(function() {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 3000);
        }
        
        // 移除加载提示
        function removeLoadingTip() {
            var loadingTip = document.getElementById('searchLoadingTip');
            if (loadingTip && document.body.contains(loadingTip)) {
                document.body.removeChild(loadingTip);
            }
        }
        
        // 搜索地址
        function searchAddress() {
            console.log("searchAddress函数被调用，重定向到doAddressSearch...");
            doAddressSearch();
        }
        
        // 根据位置更新地址
        function updateAddressFromLocation(position) {
            try {
                // 确保position是有效的LngLat对象
                var lnglatObj;
                if (position instanceof AMap.LngLat) {
                    lnglatObj = position;
                } else if (typeof position === 'object' && position.lng && position.lat) {
                    lnglatObj = new AMap.LngLat(position.lng, position.lat);
                } else if (typeof position === 'object' && typeof position.getLng === 'function' && typeof position.getLat === 'function') {
                    lnglatObj = position;
                } else if (Array.isArray(position) && position.length >= 2) {
                    lnglatObj = new AMap.LngLat(position[0], position[1]);
                } else {
                    console.error("无效的位置对象:", position);
                    fallbackToManualInput(position);
                    return;
                }
                
                var lng = lnglatObj.getLng();
                var lat = lnglatObj.getLat();
                console.log("正在解析坐标:", lng, lat);
                
                // 显示加载指示器
                var loadingTip = document.createElement('div');
                loadingTip.className = 'alert alert-info';
                loadingTip.style.position = 'fixed';
                loadingTip.style.top = '10px';
                loadingTip.style.left = '50%';
                loadingTip.style.transform = 'translateX(-50%)';
                loadingTip.style.padding = '10px 20px';
                loadingTip.style.zIndex = '99999';
                loadingTip.innerHTML = '正在获取地址信息...';
                document.body.appendChild(loadingTip);
                
                // 创建地理编码实例 - 确保插件已加载
                AMap.plugin(['AMap.Geocoder'], function() {
                    try {
                        var geocoderInstance = new AMap.Geocoder({
                            radius: 1000,           // 查找范围，单位：米
                            extensions: 'all',      // 返回完整地址信息
                            batch: false            // 单点查询
                        });
                        
                        console.log("AMap.Geocoder实例创建成功");
                        
                        // 调用逆地理编码接口
                        geocoderInstance.getAddress(lnglatObj, function(status, result) {
                            console.log("逆地理编码返回状态:", status);
                            console.log("逆地理编码返回结果:", result);
                            
                            // 移除加载指示器
                            if (document.body.contains(loadingTip)) {
                                document.body.removeChild(loadingTip);
                            }
                            
                            if (status === 'complete' && result.info === 'OK') {
                                if (result.regeocode && result.regeocode.formattedAddress) {
                                    var address = result.regeocode.formattedAddress;
                                    document.getElementById('<%= txtAddress.ClientID %>').value = address;
                                    console.log("成功获取地址:", address);
                                    
                                    // 显示成功提示
                                    var successTip = document.createElement('div');
                                    successTip.className = 'alert alert-success';
                                    successTip.style.position = 'fixed';
                                    successTip.style.top = '10px';
                                    successTip.style.left = '50%';
                                    successTip.style.transform = 'translateX(-50%)';
                                    successTip.style.padding = '10px 20px';
                                    successTip.style.zIndex = '99999';
                                    successTip.innerHTML = '地址解析成功!';
                                    document.body.appendChild(successTip);
                                    
                                    setTimeout(function() {
                                        if (document.body.contains(successTip)) {
                                            document.body.removeChild(successTip);
                                        }
                                    }, 3000);
                                } else {
                                    console.error("逆地理编码结果中无formattedAddress");
                                    tryDirectApiCall(lnglatObj);
                                }
                            } else {
                                console.error("逆地理编码失败:", status, result ? result.info : "未知错误");
                                tryDirectApiCall(lnglatObj);
                            }
                        });
                    } catch (err) {
                        console.error("使用AMap.Geocoder时出错:", err);
                        
                        // 移除加载指示器
                        if (document.body.contains(loadingTip)) {
                            document.body.removeChild(loadingTip);
                        }
                        
                        tryDirectApiCall(lnglatObj);
                    }
                });
            } catch (err) {
                console.error("解析位置时出错:", err);
                fallbackToManualInput(position);
            }
        }
        
        // 尝试直接调用API
        function tryDirectApiCall(position) {
            console.log("尝试直接调用API进行逆地理编码");
            
            try {
                var lng = position.getLng();
                var lat = position.getLat();
                
                // 显示加载指示器
                var loadingTip = document.createElement('div');
                loadingTip.className = 'alert alert-info';
                loadingTip.style.position = 'fixed';
                loadingTip.style.top = '10px';
                loadingTip.style.left = '50%';
                loadingTip.style.transform = 'translateX(-50%)';
                loadingTip.style.padding = '10px 20px';
                loadingTip.style.zIndex = '99999';
                loadingTip.innerHTML = '正在尝试其他方式获取地址...';
                document.body.appendChild(loadingTip);
                
                // 使用JSONP方式直接调用API
                var script = document.createElement('script');
                var callbackName = 'geocodeCallback_' + Math.round(Math.random() * 1000000);
                
                window[callbackName] = function(result) {
                    console.log("直接API调用结果:", result);
                    
                    // 移除加载指示器
                    if (document.body.contains(loadingTip)) {
                        document.body.removeChild(loadingTip);
                    }
                    
                    if (result && result.status === '1' && result.regeocode && result.regeocode.formatted_address) {
                        var address = result.regeocode.formatted_address;
                        document.getElementById('<%= txtAddress.ClientID %>').value = address;
                        console.log("API调用成功获取地址:", address);
                        
                        // 显示成功提示
                        var successTip = document.createElement('div');
                        successTip.className = 'alert alert-success';
                        successTip.style.position = 'fixed';
                        successTip.style.top = '10px';
                        successTip.style.left = '50%';
                        successTip.style.transform = 'translateX(-50%)';
                        successTip.style.padding = '10px 20px';
                        successTip.style.zIndex = '99999';
                        successTip.innerHTML = '地址解析成功!';
                        document.body.appendChild(successTip);
                        
                        setTimeout(function() {
                            if (document.body.contains(successTip)) {
                                document.body.removeChild(successTip);
                            }
                        }, 3000);
                    } else {
                        console.error("直接API调用失败:", result ? result.info : "未知错误");
                        
                        // 作为最后的手段，尝试从AMap获取省市信息
                        tryGetProvinceCity(position);
                    }
                        
                        // 清理全局回调函数
                        delete window[callbackName];
                        // 移除script标签
                        if (script.parentNode) {
                            script.parentNode.removeChild(script);
                    }
                };
                
                // 构建API URL，添加更多参数以增加成功率
                var apiUrl = 'https://restapi.amap.com/v3/geocode/regeo?key=95a8d6c364456acd37faf8d0d8cfbc04' +
                        '&location=' + lng + ',' + lat +
                        '&extensions=all' +
                        '&radius=1000' +
                        '&roadlevel=0' +
                        '&output=json&callback=' + callbackName;
                
                console.log("直接调用API URL:", apiUrl);
                
                script.src = apiUrl;
                document.body.appendChild(script);
                
                // 设置10秒超时
                setTimeout(function() {
                    if (window[callbackName]) {
                        console.error("直接API调用超时");
                        delete window[callbackName];
                        
                        if (document.body.contains(script)) {
                            document.body.removeChild(script);
                        }
                        
                        if (document.body.contains(loadingTip)) {
                            document.body.removeChild(loadingTip);
                        }
                        
                        // 尝试获取省市信息
                        tryGetProvinceCity(position);
                    }
                }, 10000);
            } catch (err) {
                console.error("直接API调用时出错:", err);
                
                // 尝试获取省市信息
                tryGetProvinceCity(position);
            }
        }
        
        // 尝试从AMap获取省市信息
        function tryGetProvinceCity(position) {
            console.log("尝试获取省市信息");
            
            try {
                    AMap.plugin('AMap.CitySearch', function() {
                        var citySearch = new AMap.CitySearch();
                    
                        citySearch.getLocalCity(function(status, result) {
                        console.log("城市查询状态:", status);
                        console.log("城市查询结果:", result);
                        
                        if (status === 'complete' && result && result.city && result.province) {
                            // 尝试再次使用这些信息和坐标进行逆地理编码
                            var lng = position.getLng();
                            var lat = position.getLat();
                            
                            // 构造地址
                            var address = result.province;
                            if (result.province !== result.city) {
                                address += result.city;
                            }
                            
                            // 添加坐标
                            address += " (坐标: " + lng.toFixed(6) + "," + lat.toFixed(6) + ")";
                            
                            // 设置地址
                                document.getElementById('<%= txtAddress.ClientID %>').value = address;
                                
                            // 提示用户补充详细地址
                                var warningTip = document.createElement('div');
                                warningTip.className = 'alert alert-warning';
                                warningTip.style.position = 'fixed';
                                warningTip.style.top = '10px';
                                warningTip.style.left = '50%';
                                warningTip.style.transform = 'translateX(-50%)';
                                warningTip.style.padding = '10px 20px';
                                warningTip.style.zIndex = '99999';
                            warningTip.innerHTML = '只能获取到城市信息，请手动补充详细地址';
                                document.body.appendChild(warningTip);
                                
                                setTimeout(function() {
                                if (document.body.contains(warningTip)) {
                                    document.body.removeChild(warningTip);
                                }
                                }, 5000);
                            } else {
                            // 最后的回退
                                fallbackToManualInput(position);
                            }
                        });
                    });
            } catch (err) {
                console.error("获取省市信息时出错:", err);
                fallbackToManualInput(position);
            }
        }
        
        // 获取当前位置
        function getCurrentLocation() {
            console.log("开始获取当前位置...");
            
            // 确保地图显示并初始化
            if (!isMapInitialized) {
                initMap();
            }
            
            document.getElementById('mapContainer').style.display = 'block';
            document.getElementById('mapSearchBox').style.display = 'block';
            
            // 显示加载提示
            showLoadingTip("正在获取您的位置...");
            
            // 创建定位标志
            var locationSuccess = false;
            
            // 检查环境
            var isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            console.log("当前环境 - HTTPS/本地:", isSecure);
            
            // 使用标准高德地图定位API
            AMap.plugin('AMap.Geolocation', function() {
                // 创建定位对象
                var geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true,    // 使用高精度定位
                    timeout: 10000,              // 超时设置
                    maximumAge: 0,               // 定位结果缓存0毫秒
                    convert: true,               // 自动偏移坐标为高德坐标系
                    showButton: false,           // 不显示定位按钮
                    buttonPosition: 'RB',        // 按钮位置，右下角
                    buttonOffset: new AMap.Pixel(10, 20), // 按钮偏移量
                    showMarker: false,           // 不显示定位点
                    showCircle: false,           // 不显示精度圈
                    panToLocation: false,        // 不移动到定位点
                    zoomToAccuracy: false        // 不调整视野到精度范围内
                });
                
                // 将定位控件添加到地图
                map.addControl(geolocation);
                console.log("Geolocation控件添加到地图");
                
                // 设置定位超时
                var timeoutId = setTimeout(function() {
                    if (!locationSuccess) {
                        console.warn("定位请求超时，尝试IP定位");
                        onLocationError({info: "TIMEOUT", message: "定位请求超时"});
                    }
                }, 12000);
                
                // 获取当前位置，使用回调函数而不是事件监听器
                geolocation.getCurrentPosition(function(status, result) {
                    // 清除超时
                    clearTimeout(timeoutId);
                    
                    if (status === 'complete') {
                        // 定位成功
                        locationSuccess = true;
                        
                        console.log("定位成功 - 结果:", result);
                        
                        // 从定位结果提取信息
                        var position = result.position;       // 位置对象
                        var lng = position.getLng();        // 经度
                        var lat = position.getLat();        // 纬度
                        var accuracy = result.accuracy;       // 精度（米）
                        var formattedAddress = result.formattedAddress || ''; // 结构化地址
                        var addressComponent = result.addressComponent || {}; // 地址组件
                        
                        console.log("定位详情 - 经度:", lng, "纬度:", lat, 
                                    "精度:", accuracy, "米", 
                                    "地址:", formattedAddress);
                        
                        // 优先使用定位返回的结构化地址
                        var address = formattedAddress;
                        
                        // 如果没有完整地址但有地址组件，则构建地址
                        if (!address && addressComponent) {
                            address = buildAddress(addressComponent);
                        }
                        
                        // 更新地图和位置信息
                        updateMapWithLocation(lng, lat, address);
                        
                        // 显示成功通知
                        hideLoadingTip();
                        showNotification('success', '定位成功!');
                    } else {
                        // 定位失败
                        console.warn("定位失败:", result.info, result.message);
                        
                        // 根据错误类型提供不同的处理
                        switch (result.info) {
                            case 'PERMISSION_DENIED':
                                showNotification('warning', '定位权限被拒绝，请在浏览器设置中允许获取位置');
                                break;
                            case 'TIMEOUT':
                                showNotification('warning', '定位请求超时');
                                break;
                            default:
                                // 不显示通知，因为即将尝试备选方案
                                console.log("尝试使用备选定位方案...");
                        }
                        
                        // 尝试IP定位
                        tryIPLocation();
                    }
                });
            });
            
            // 从地址组件构建地址字符串
            function buildAddress(component) {
                var address = '';
                if (component.province) {
                    address += component.province;
                }
                if (component.city && component.city !== component.province) {
                    address += component.city;
                }
                if (component.district) {
                    address += component.district;
                }
                if (component.township) {
                    address += component.township;
                }
                if (component.street) {
                    address += component.street;
                }
                if (component.streetNumber) {
                    address += component.streetNumber;
                }
                return address;
            }
            
            // IP定位作为备选方案
            function tryIPLocation() {
                console.log("使用IP定位...");
                
                // 如果已定位成功，不再执行
                if (locationSuccess) return;
                
                // 使用城市查询API
                AMap.plugin('AMap.CitySearch', function() {
                    var citySearch = new AMap.CitySearch();
                    
                    citySearch.getLocalCity(function(status, result) {
                        // 隐藏加载提示
                        hideLoadingTip();
                        
                        if (status === 'complete' && result && result.center) {
                            // 定位成功
                            locationSuccess = true;
                            
                            // 获取城市信息
                            var lng = result.center[0];
                            var lat = result.center[1];
                            var cityName = result.city;
                            var provinceName = result.province;
                            
                            console.log("IP定位成功 - 城市:", cityName, "坐标:", lng, lat);
                            
                            // 构建地址
                            var address = provinceName;
                            if (provinceName !== cityName) {
                                address += cityName;
                            }
                            
                            // 更新地图和位置信息
                            updateMapWithLocation(lng, lat, address);
                            
                            // 提示用户
                            showNotification('warning', '只能获取到城市级位置，请补充详细地址');
                        } else {
                            // IP定位失败
                            console.error("IP定位失败:", result ? result.info : "未知错误");
                            showNotification('error', '无法获取您的位置，请手动选择位置或输入地址');
                            
                            // 在地图上显示默认位置（中国中心）
                            var defaultLng = 116.397428;
                            var defaultLat = 39.90923;
                            map.setCenter(new AMap.LngLat(defaultLng, defaultLat));
                            map.setZoom(5); // 显示整个中国
                        }
                    });
                });
            }
            
            // 更新地图和位置信息
            function updateMapWithLocation(lng, lat, address) {
                // 创建位置对象
                var position = new AMap.LngLat(lng, lat);
                
                // 更新地图
                map.setCenter(position);
                map.setZoom(16);
                marker.setPosition(position);
                
                // 更新隐藏字段
                document.getElementById('<%= hfLongitude.ClientID %>').value = lng;
                document.getElementById('<%= hfLatitude.ClientID %>').value = lat;
                
                // 隐藏加载提示
                hideLoadingTip();
                
                // 更新地址字段
                if (address) {
                    document.getElementById('<%= txtAddress.ClientID %>').value = address;
                } else {
                    // 如果没有地址，使用逆地理编码获取地址
                    getAddressFromLocation(lng, lat);
                }
            }
            
            // 通过坐标获取地址（逆地理编码）
            function getAddressFromLocation(lng, lat) {
                console.log("通过坐标获取地址...");
                
                AMap.plugin('AMap.Geocoder', function() {
                    var geocoder = new AMap.Geocoder({
                        radius: 1000,
                        extensions: 'all'
                    });
                    
                    geocoder.getAddress(new AMap.LngLat(lng, lat), function(status, result) {
                        if (status === 'complete' && result.info === 'OK' && result.regeocode) {
                            var address = result.regeocode.formattedAddress;
                            console.log("逆地理编码成功 - 地址:", address);
                            document.getElementById('<%= txtAddress.ClientID %>').value = address;
                        } else {
                            console.warn("逆地理编码失败:", status);
                            var coordsStr = lng.toFixed(6) + "," + lat.toFixed(6);
                            document.getElementById('<%= txtAddress.ClientID %>').value = "位置坐标: " + coordsStr;
                            showNotification('warning', '无法获取详细地址，请手动输入');
                        }
                    });
                });
            }
            
            // 显示加载提示
            function showLoadingTip(message) {
                // 移除可能存在的提示
                hideLoadingTip();
                
                // 创建新提示
                var loadingTip = document.createElement('div');
                loadingTip.id = 'locationLoadingTip';
                loadingTip.className = 'alert alert-info';
                loadingTip.style.position = 'fixed';
                loadingTip.style.top = '10px';
                loadingTip.style.left = '50%';
                loadingTip.style.transform = 'translateX(-50%)';
                loadingTip.style.padding = '10px 20px';
                loadingTip.style.zIndex = '99999';
                loadingTip.innerHTML = message || '正在加载...';
                document.body.appendChild(loadingTip);
            }
            
            // 隐藏加载提示
            function hideLoadingTip() {
                var loadingTip = document.getElementById('locationLoadingTip');
                if (loadingTip && document.body.contains(loadingTip)) {
                    document.body.removeChild(loadingTip);
                }
            }
        }

        // 当所有地址解析方法都失败时，回退到手动输入
        function fallbackToManualInput(position) {
            try {
                var lng, lat;
                if (position instanceof AMap.LngLat) {
                    lng = position.getLng();
                    lat = position.getLat();
                } else if (typeof position === 'object' && position.lng && position.lat) {
                    lng = position.lng;
                    lat = position.lat;
                } else if (typeof position === 'object' && typeof position.getLng === 'function') {
                    lng = position.getLng();
                    lat = position.getLat();
                } else if (Array.isArray(position) && position.length >= 2) {
                    lng = position[0];
                    lat = position[1];
                } else {
                    lng = 0;
                    lat = 0;
                }
                
                var coordsStr = lng.toFixed(6) + "," + lat.toFixed(6);
                document.getElementById('<%= txtAddress.ClientID %>').value = "请输入详细地址 (坐标: " + coordsStr + ")";
                document.getElementById('<%= txtAddress.ClientID %>').select();
                document.getElementById('<%= txtAddress.ClientID %>').focus();
                
                console.log("所有方法都失败，回退到手动输入");
                
                // 显示提示
                var warningTip = document.createElement('div');
                warningTip.className = 'alert alert-warning';
                warningTip.style.position = 'fixed';
                warningTip.style.top = '10px';
                warningTip.style.left = '50%';
                warningTip.style.transform = 'translateX(-50%)';
                warningTip.style.padding = '10px 20px';
                warningTip.style.zIndex = '99999';
                warningTip.innerHTML = '无法获取详细地址，请手动输入';
                document.body.appendChild(warningTip);
                
                // 5秒后自动移除提示
                setTimeout(function() {
                    document.body.removeChild(warningTip);
                }, 5000);
            } catch (e) {
                console.error("回退方法出错:", e);
                document.getElementById('<%= txtAddress.ClientID %>').value = "请输入详细地址";
            }
        }
    </script>
</asp:Content> 