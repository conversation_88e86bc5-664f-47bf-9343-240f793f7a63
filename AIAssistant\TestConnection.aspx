<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TestConnection.aspx.cs" Inherits="AIAssistant_TestConnection" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>连接测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .loading { border-left-color: #ffc107; background: #fff3cd; }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>🔧 AI助手连接测试</h1>
        
        <div>
            <h3>基础连接测试</h3>
            <asp:Button ID="btnTestAI" runat="server" Text="测试AI服务" OnClick="btnTestAI_Click" />
            <asp:Button ID="btnTestAmap" runat="server" Text="测试高德地图" OnClick="btnTestAmap_Click" />
            <asp:Button ID="btnTestDB" runat="server" Text="测试数据库" OnClick="btnTestDB_Click" />
        </div>
        
        <div>
            <h3>功能测试</h3>
            <asp:Button ID="btnTestGeocode" runat="server" Text="测试地理编码" OnClick="btnTestGeocode_Click" />
            <asp:Button ID="btnTestRoute" runat="server" Text="测试路线规划" OnClick="btnTestRoute_Click" />
            <asp:Button ID="btnTestShops" runat="server" Text="测试维修店搜索" OnClick="btnTestShops_Click" />
        </div>
        
        <div>
            <h3>测试结果</h3>
            <asp:Literal ID="litResults" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
