<%@ Page Title="聊天管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ChatManagement.aspx.cs" Inherits="Admin_ChatManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <style>
        .user-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .user-muted {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .mute-info {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 8px;
            margin-top: 10px;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-shield-alt"></i> 聊天管理</h2>
                <p class="text-muted">管理聊天系统，包括用户禁言、消息管理等</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="row mb-3">
            <div class="col-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
            </div>
        </asp:Panel>

        <!-- 导航标签 -->
        <ul class="nav nav-tabs mb-4" id="chatManagementTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="users-tab" data-toggle="tab" href="#users" role="tab">
                    <i class="fas fa-users"></i> 用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="rooms-tab" data-toggle="tab" href="#rooms" role="tab">
                    <i class="fas fa-comments"></i> 聊天室管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="messages-tab" data-toggle="tab" href="#messages" role="tab">
                    <i class="fas fa-comment-dots"></i> 消息管理
                </a>
            </li>
        </ul>

        <div class="tab-content" id="chatManagementTabsContent">
            <!-- 用户管理标签页 -->
            <div class="tab-pane fade show active" id="users" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <asp:TextBox ID="txtSearchUser" runat="server" CssClass="form-control" placeholder="搜索用户名或邮箱"></asp:TextBox>
                            <div class="input-group-append">
                                <asp:Button ID="btnSearchUser" runat="server" Text="搜索" CssClass="btn btn-outline-secondary" OnClick="btnSearchUser_Click" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <asp:DropDownList ID="ddlUserTypeFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlUserTypeFilter_SelectedIndexChanged">
                            <asp:ListItem Value="">所有用户类型</asp:ListItem>
                            <asp:ListItem Value="CarOwner">车主</asp:ListItem>
                            <asp:ListItem Value="RepairShop">维修店</asp:ListItem>
                        </asp:DropDownList>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <asp:GridView ID="gvUsers" runat="server" AutoGenerateColumns="False" 
                            CssClass="table table-striped table-hover" DataKeyNames="UserID"
                            OnRowCommand="gvUsers_RowCommand" EmptyDataText="没有找到用户">
                            <Columns>
                                <asp:BoundField DataField="Username" HeaderText="用户名" />
                                <asp:BoundField DataField="Email" HeaderText="邮箱" />
                                <asp:TemplateField HeaderText="用户类型">
                                    <ItemTemplate>
                                        <span class='<%# GetUserTypeBadgeClass(Eval("UserType").ToString()) %>'>
                                            <%# GetUserTypeDisplayName(Eval("UserType").ToString()) %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="禁言状态">
                                    <ItemTemplate>
                                        <%# GetMuteStatusDisplay(Convert.ToInt32(Eval("UserID"))) %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <div class="btn-group">
                                            <asp:LinkButton ID="lbtnMuteUser" runat="server" 
                                                CssClass="btn btn-sm btn-warning"
                                                CommandName="MuteUser" CommandArgument='<%# Eval("UserID") %>' 
                                                ToolTip="禁言用户">
                                                <i class="fas fa-ban"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnUnmuteUser" runat="server" 
                                                CssClass="btn btn-sm btn-success"
                                                CommandName="UnmuteUser" CommandArgument='<%# Eval("UserID") %>' 
                                                ToolTip="解除禁言">
                                                <i class="fas fa-check"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnViewMessages" runat="server" 
                                                CssClass="btn btn-sm btn-info"
                                                CommandName="ViewMessages" CommandArgument='<%# Eval("UserID") %>' 
                                                ToolTip="查看消息">
                                                <i class="fas fa-eye"></i>
                                            </asp:LinkButton>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>

            <!-- 聊天室管理标签页 -->
            <div class="tab-pane fade" id="rooms" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <asp:GridView ID="gvChatRooms" runat="server" AutoGenerateColumns="False" 
                            CssClass="table table-striped table-hover" DataKeyNames="RoomID"
                            OnRowCommand="gvChatRooms_RowCommand" EmptyDataText="没有聊天室">
                            <Columns>
                                <asp:BoundField DataField="RoomName" HeaderText="聊天室名称" />
                                <asp:TemplateField HeaderText="类型">
                                    <ItemTemplate>
                                        <span class='<%# Eval("RoomType").ToString() == "Public" ? "badge badge-success" : "badge badge-info" %>'>
                                            <%# Eval("RoomType").ToString() == "Public" ? "公共聊天" : "私人聊天" %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="MessageCount" HeaderText="消息数量" />
                                <asp:BoundField DataField="MemberCount" HeaderText="成员数量" />
                                <asp:BoundField DataField="CreatedDate" HeaderText="创建时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <div class="btn-group">
                                            <asp:LinkButton ID="lbtnViewRoomMessages" runat="server" 
                                                CssClass="btn btn-sm btn-info"
                                                CommandName="ViewRoomMessages" CommandArgument='<%# Eval("RoomID") %>' 
                                                ToolTip="查看消息">
                                                <i class="fas fa-eye"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnManageRoom" runat="server" 
                                                CssClass="btn btn-sm btn-primary"
                                                CommandName="ManageRoom" CommandArgument='<%# Eval("RoomID") %>' 
                                                ToolTip="管理聊天室">
                                                <i class="fas fa-cog"></i>
                                            </asp:LinkButton>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>

            <!-- 消息管理标签页 -->
            <div class="tab-pane fade" id="messages" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <asp:DropDownList ID="ddlRoomFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlRoomFilter_SelectedIndexChanged">
                            <asp:ListItem Value="">所有聊天室</asp:ListItem>
                        </asp:DropDownList>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <asp:CheckBox ID="chkShowDeletedMessages" runat="server" CssClass="form-check-input" AutoPostBack="true" OnCheckedChanged="chkShowDeletedMessages_CheckedChanged" />
                            <label class="form-check-label">显示已删除的消息</label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <asp:GridView ID="gvMessages" runat="server" AutoGenerateColumns="False" 
                            CssClass="table table-striped table-hover" DataKeyNames="MessageID"
                            OnRowCommand="gvMessages_RowCommand" EmptyDataText="没有消息" AllowPaging="true" PageSize="20" OnPageIndexChanging="gvMessages_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="RoomName" HeaderText="聊天室" />
                                <asp:BoundField DataField="SenderName" HeaderText="发送者" />
                                <asp:TemplateField HeaderText="消息内容">
                                    <ItemTemplate>
                                        <div class='<%# Convert.ToBoolean(Eval("IsDeleted")) ? "text-muted font-italic" : "" %>'>
                                            <%# Convert.ToBoolean(Eval("IsDeleted")) ? "[已删除]" : Eval("MessageContent").ToString() %>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="SentDate" HeaderText="发送时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <span class='<%# Convert.ToBoolean(Eval("IsDeleted")) ? "badge badge-danger" : "badge badge-success" %>'>
                                            <%# Convert.ToBoolean(Eval("IsDeleted")) ? "已删除" : "正常" %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnDeleteMessage" runat="server" 
                                            CssClass="btn btn-sm btn-danger"
                                            CommandName="DeleteMessage" CommandArgument='<%# Eval("MessageID") %>' 
                                            ToolTip="删除消息" Visible='<%# !Convert.ToBoolean(Eval("IsDeleted")) %>'
                                            OnClientClick="return confirm('确定要删除这条消息吗？');">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <!-- 禁言模态框 -->
        <div class="modal fade" id="muteUserModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">禁言用户</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <asp:HiddenField ID="hfMuteUserID" runat="server" />
                        
                        <div class="form-group">
                            <label>禁言范围：</label>
                            <div>
                                <asp:RadioButton ID="rbGlobalMute" runat="server" GroupName="MuteScope"
                                    Text="全局禁言（所有聊天室）" Checked="true" CssClass="form-check-input" />
                                <br />
                                <asp:RadioButton ID="rbPublicMute" runat="server" GroupName="MuteScope"
                                    Text="仅公共聊天室" CssClass="form-check-input" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label>禁言时长：</label>
                            <asp:DropDownList ID="ddlMuteDuration" runat="server" CssClass="form-control">
                                <asp:ListItem Value="1">1小时</asp:ListItem>
                                <asp:ListItem Value="24">1天</asp:ListItem>
                                <asp:ListItem Value="168">1周</asp:ListItem>
                                <asp:ListItem Value="720">1个月</asp:ListItem>
                                <asp:ListItem Value="0">永久禁言</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <div class="form-group">
                            <label>禁言原因：</label>
                            <asp:TextBox ID="txtMuteReason" runat="server" CssClass="form-control" 
                                TextMode="MultiLine" Rows="3" placeholder="请输入禁言原因..."></asp:TextBox>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <asp:Button ID="btnConfirmMute" runat="server" Text="确认禁言" 
                            CssClass="btn btn-warning" OnClick="btnConfirmMute_Click" />
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
