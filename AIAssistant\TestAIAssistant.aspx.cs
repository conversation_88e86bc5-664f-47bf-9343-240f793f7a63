using System;
using System.Web.UI;
using System.Web.Services;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Linq;

public partial class AIAssistant_TestAIAssistant : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 测试页面不需要登录验证
    }

    /// <summary>
    /// 测试AI服务连接
    /// </summary>
    [WebMethod]
    public static async Task<string> TestAIConnection()
    {
        try
        {
            var response = await AIService.GetAIResponseAsync("测试连接", "你是一个测试助手，请简单回复'连接正常'");
            
            if (!string.IsNullOrEmpty(response) && !response.Contains("抱歉"))
            {
                return JsonConvert.SerializeObject(new { success = true, message = "AI服务连接正常", response = response });
            }
            else
            {
                return JsonConvert.SerializeObject(new { success = false, message = "AI服务响应异常", response = response });
            }
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试高德地图API连接
    /// </summary>
    [WebMethod]
    public static async Task<string> TestAmapConnection()
    {
        try
        {
            var location = await AmapService.GeocodeAsync("北京市天安门");
            
            if (location != null)
            {
                return JsonConvert.SerializeObject(new { 
                    success = true, 
                    message = "高德地图API连接正常",
                    location = location
                });
            }
            else
            {
                return JsonConvert.SerializeObject(new { success = false, message = "高德地图API响应异常" });
            }
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试地理编码
    /// </summary>
    [WebMethod]
    public static async Task<string> TestGeocode(string address)
    {
        try
        {
            var location = await AmapService.GeocodeAsync(address);
            
            if (location != null)
            {
                return JsonConvert.SerializeObject(new { success = true, location = location });
            }
            else
            {
                return JsonConvert.SerializeObject(new { success = false, message = "地理编码失败" });
            }
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试意图识别
    /// </summary>
    [WebMethod]
    public static async Task<string> TestIntentAnalysis(string message)
    {
        try
        {
            var intent = await AIService.AnalyzeIntentAsync(message);
            return JsonConvert.SerializeObject(new { success = true, intent = intent });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试路线优化
    /// </summary>
    [WebMethod]
    public static async Task<string> TestRouteOptimization(double userLongitude, double userLatitude)
    {
        try
        {
            var recommendations = await RouteOptimizer.GetOptimizedShopRecommendationsAsync(
                userLongitude, userLatitude, new RoutePreferences { MaxDistanceKm = 10 });
            
            return JsonConvert.SerializeObject(new { 
                success = true, 
                count = recommendations.Count,
                recommendations = recommendations.Take(3).ToList()
            });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试数据库连接和维修店数据
    /// </summary>
    [WebMethod]
    public static string TestDatabaseConnection()
    {
        try
        {
            var shops = ShopManager.GetAllShopsForFrontend();
            var shopsWithLocation = 0;
            
            foreach (System.Data.DataRow shop in shops.Rows)
            {
                if (shop["Longitude"] != DBNull.Value && shop["Latitude"] != DBNull.Value)
                {
                    shopsWithLocation++;
                }
            }
            
            return JsonConvert.SerializeObject(new { 
                success = true, 
                totalShops = shops.Rows.Count,
                shopsWithLocation = shopsWithLocation,
                message = $"数据库连接正常，共有{shops.Rows.Count}个维修店，其中{shopsWithLocation}个有地理位置信息"
            });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 批量测试AI响应性能
    /// </summary>
    [WebMethod]
    public static async Task<string> BatchTestAIPerformance(int testCount = 5)
    {
        try
        {
            var testMessages = new[]
            {
                "我想找最近的维修店",
                "我的车发动机有异响",
                "保养需要多少钱",
                "什么时候需要保养",
                "从北京到上海的路线"
            };

            var results = new System.Collections.Generic.List<object>();
            var totalTime = 0;

            for (int i = 0; i < Math.Min(testCount, testMessages.Length); i++)
            {
                var startTime = DateTime.Now;
                var response = await AIService.GetCarRepairAdviceAsync(testMessages[i]);
                var endTime = DateTime.Now;
                var responseTime = (int)(endTime - startTime).TotalMilliseconds;
                
                totalTime += responseTime;
                
                results.Add(new
                {
                    message = testMessages[i],
                    responseTime = responseTime,
                    responseLength = response?.Length ?? 0,
                    success = !string.IsNullOrEmpty(response) && !response.Contains("抱歉")
                });
            }

            return JsonConvert.SerializeObject(new
            {
                success = true,
                testCount = results.Count,
                averageResponseTime = totalTime / results.Count,
                totalTime = totalTime,
                results = results
            });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试完整的AI助手流程
    /// </summary>
    [WebMethod]
    public static async Task<string> TestCompleteFlow(string testScenario)
    {
        try
        {
            var scenarios = new System.Collections.Generic.Dictionary<string, object>
            {
                ["route_planning"] = new { message = "我在北京天安门，想去最近的维修店", location = "116.397428,39.90923" },
                ["fault_diagnosis"] = new { message = "我的车发动机有异响，怎么办", location = "" },
                ["shop_recommendation"] = new { message = "推荐一个好的维修店", location = "116.397428,39.90923" },
                ["price_inquiry"] = new { message = "换机油需要多少钱", location = "" }
            };

            if (!scenarios.ContainsKey(testScenario))
            {
                return JsonConvert.SerializeObject(new { success = false, message = "未知的测试场景" });
            }

            dynamic scenario = scenarios[testScenario];
            var startTime = DateTime.Now;
            
            // 模拟用户ID
            var userID = 1;
            
            var response = await ChatAssistantManager.ProcessUserMessageAsync(
                scenario.message.ToString(), 
                userID, 
                scenario.location.ToString());
            
            var endTime = DateTime.Now;
            var responseTime = (int)(endTime - startTime).TotalMilliseconds;

            return JsonConvert.SerializeObject(new
            {
                success = true,
                scenario = testScenario,
                responseTime = responseTime,
                response = response
            });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// 获取系统状态概览
    /// </summary>
    [WebMethod]
    public static async Task<string> GetSystemStatus()
    {
        try
        {
            var status = new
            {
                timestamp = DateTime.Now,
                aiService = await TestAIServiceStatus(),
                amapService = await TestAmapServiceStatus(),
                database = TestDatabaseStatus(),
                memoryUsage = GC.GetTotalMemory(false) / 1024 / 1024, // MB
                serverTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            return JsonConvert.SerializeObject(new { success = true, status = status });
        }
        catch (Exception ex)
        {
            return JsonConvert.SerializeObject(new { success = false, message = ex.Message });
        }
    }

    private static async Task<bool> TestAIServiceStatus()
    {
        try
        {
            var response = await AIService.GetAIResponseAsync("ping", "请回复pong");
            return !string.IsNullOrEmpty(response);
        }
        catch
        {
            return false;
        }
    }

    private static async Task<bool> TestAmapServiceStatus()
    {
        try
        {
            var location = await AmapService.GeocodeAsync("北京");
            return location != null;
        }
        catch
        {
            return false;
        }
    }

    private static bool TestDatabaseStatus()
    {
        try
        {
            var shops = ShopManager.GetAllShopsForFrontend();
            return shops.Rows.Count >= 0;
        }
        catch
        {
            return false;
        }
    }
}
