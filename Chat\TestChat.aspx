<%@ Page Title="聊天系统测试" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="TestChat.aspx.cs" Inherits="Chat_TestChat" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial"></i> 聊天系统测试</h2>
                <p class="text-muted">测试聊天系统的基本功能</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="row mb-3">
            <div class="col-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
            </div>
        </asp:Panel>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> 数据库测试</h5>
                    </div>
                    <div class="card-body">
                        <asp:Button ID="btnTestDatabase" runat="server" Text="测试数据库连接" 
                            CssClass="btn btn-primary mb-2" OnClick="btnTestDatabase_Click" />
                        <br />
                        <asp:Button ID="btnTestChatRooms" runat="server" Text="测试聊天室查询" 
                            CssClass="btn btn-info mb-2" OnClick="btnTestChatRooms_Click" />
                        <br />
                        <asp:Button ID="btnTestUsers" runat="server" Text="测试用户查询" 
                            CssClass="btn btn-success mb-2" OnClick="btnTestUsers_Click" />
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-comments"></i> 聊天功能测试</h5>
                    </div>
                    <div class="card-body">
                        <asp:Button ID="btnTestCreatePrivateChat" runat="server" Text="测试创建私聊" 
                            CssClass="btn btn-warning mb-2" OnClick="btnTestCreatePrivateChat_Click" />
                        <br />
                        <asp:Button ID="btnTestSendMessage" runat="server" Text="测试发送消息" 
                            CssClass="btn btn-danger mb-2" OnClick="btnTestSendMessage_Click" />
                        <br />
                        <asp:Button ID="btnTestMuteUser" runat="server" Text="测试禁言功能" 
                            CssClass="btn btn-dark mb-2" OnClick="btnTestMuteUser_Click" />
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 测试结果</h5>
                    </div>
                    <div class="card-body">
                        <asp:TextBox ID="txtTestResults" runat="server" TextMode="MultiLine" 
                            Rows="15" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-link"></i> 快速导航</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <a href="ChatMain.aspx" class="btn btn-primary btn-block mb-2">
                                    <i class="fas fa-comments"></i> 聊天主页面
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="../Admin/ChatManagement.aspx" class="btn btn-success btn-block mb-2">
                                    <i class="fas fa-shield-alt"></i> 聊天管理 (管理员)
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="../CarOwner/Dashboard.aspx" class="btn btn-info btn-block mb-2">
                                    <i class="fas fa-tachometer-alt"></i> 车主Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
