using System;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 高德地图API服务类
/// </summary>
public class AmapService
{
    private static readonly string AMAP_API_KEY = "95a8d6c364456acd37faf8d0d8cfbc04";
    private static readonly string AMAP_SECRET = "78467c9c5852e1e36eeae0c8d21c0bf9";
    private static readonly HttpClient httpClient = new HttpClient();

    /// <summary>
    /// 地理编码：将地址转换为经纬度
    /// </summary>
    /// <param name="address">地址</param>
    /// <returns>地理位置信息</returns>
    public static async Task<LocationInfo> GeocodeAsync(string address)
    {
        try
        {
            var url = $"https://restapi.amap.com/v3/geocode/geo?key={AMAP_API_KEY}&address={Uri.EscapeDataString(address)}";
            var response = await httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<GeocodeResponse>(response);

            if (result?.status == "1" && result.geocodes?.Length > 0)
            {
                var geocode = result.geocodes[0];
                var location = geocode.location.Split(',');
                return new LocationInfo
                {
                    Longitude = double.Parse(location[0]),
                    Latitude = double.Parse(location[1]),
                    Address = geocode.formatted_address,
                    District = geocode.district,
                    City = geocode.city,
                    Province = geocode.province
                };
            }
            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"地理编码错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 逆地理编码：将经纬度转换为地址
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <returns>地址信息</returns>
    public static async Task<LocationInfo> ReverseGeocodeAsync(double longitude, double latitude)
    {
        try
        {
            var url = $"https://restapi.amap.com/v3/geocode/regeo?key={AMAP_API_KEY}&location={longitude},{latitude}";
            var response = await httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<ReverseGeocodeResponse>(response);

            if (result?.status == "1" && result.regeocode != null)
            {
                var regeocode = result.regeocode;
                return new LocationInfo
                {
                    Longitude = longitude,
                    Latitude = latitude,
                    Address = regeocode.formatted_address,
                    District = regeocode.addressComponent?.district,
                    City = regeocode.addressComponent?.city,
                    Province = regeocode.addressComponent?.province
                };
            }
            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"逆地理编码错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 路线规划
    /// </summary>
    /// <param name="origin">起点经纬度</param>
    /// <param name="destination">终点经纬度</param>
    /// <param name="strategy">路线策略（0-速度优先，1-费用优先，2-距离优先，3-不走高速）</param>
    /// <returns>路线信息</returns>
    public static async Task<RouteInfo> GetRouteAsync(string origin, string destination, int strategy = 0)
    {
        try
        {
            var url = $"https://restapi.amap.com/v3/direction/driving?key={AMAP_API_KEY}&origin={origin}&destination={destination}&strategy={strategy}&extensions=all";
            var response = await httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<RouteResponse>(response);

            if (result?.status == "1" && result.route?.paths?.Length > 0)
            {
                var path = result.route.paths[0];
                return new RouteInfo
                {
                    Distance = int.Parse(path.distance),
                    Duration = int.Parse(path.duration),
                    Tolls = int.Parse(path.tolls ?? "0"),
                    TrafficLights = int.Parse(path.traffic_lights ?? "0"),
                    Steps = path.steps?.Select(s => new RouteStep
                    {
                        Instruction = s.instruction,
                        Distance = int.Parse(s.distance),
                        Duration = int.Parse(s.duration),
                        Road = s.road
                    }).ToArray()
                };
            }
            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路线规划错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 搜索附近的维修店
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（米）</param>
    /// <returns>附近维修店列表</returns>
    public static async Task<List<NearbyShop>> SearchNearbyShopsAsync(double longitude, double latitude, int radius = 5000)
    {
        try
        {
            var location = $"{longitude},{latitude}";
            var keywords = "汽车维修|汽修|4S店";
            var url = $"https://restapi.amap.com/v3/place/around?key={AMAP_API_KEY}&location={location}&keywords={Uri.EscapeDataString(keywords)}&radius={radius}&extensions=all";
            
            var response = await httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<PlaceSearchResponse>(response);

            if (result?.status == "1" && result.pois != null)
            {
                return result.pois.Select(poi => new NearbyShop
                {
                    Name = poi.name,
                    Address = poi.address,
                    Phone = poi.tel,
                    Longitude = double.Parse(poi.location.Split(',')[0]),
                    Latitude = double.Parse(poi.location.Split(',')[1]),
                    Distance = int.Parse(poi.distance ?? "0"),
                    Rating = poi.biz_ext?.rating ?? "0"
                }).ToList();
            }
            return new List<NearbyShop>();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"搜索附近维修店错误: {ex.Message}");
            return new List<NearbyShop>();
        }
    }

    /// <summary>
    /// 计算两点间距离
    /// </summary>
    /// <param name="lon1">起点经度</param>
    /// <param name="lat1">起点纬度</param>
    /// <param name="lon2">终点经度</param>
    /// <param name="lat2">终点纬度</param>
    /// <returns>距离（米）</returns>
    public static double CalculateDistance(double lon1, double lat1, double lon2, double lat2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }
}

#region 数据模型

/// <summary>
/// 位置信息
/// </summary>
public class LocationInfo
{
    public double Longitude { get; set; }
    public double Latitude { get; set; }
    public string Address { get; set; }
    public string District { get; set; }
    public string City { get; set; }
    public string Province { get; set; }
}

/// <summary>
/// 路线信息
/// </summary>
public class RouteInfo
{
    public int Distance { get; set; } // 距离（米）
    public int Duration { get; set; } // 时间（秒）
    public int Tolls { get; set; } // 过路费（元）
    public int TrafficLights { get; set; } // 红绿灯数量
    public RouteStep[] Steps { get; set; } // 路线步骤
    public int Strategy { get; set; } // 路线策略
}

/// <summary>
/// 路线步骤
/// </summary>
public class RouteStep
{
    public string Instruction { get; set; } // 导航指令
    public int Distance { get; set; } // 距离（米）
    public int Duration { get; set; } // 时间（秒）
    public string Road { get; set; } // 道路名称
}

/// <summary>
/// 附近维修店
/// </summary>
public class NearbyShop
{
    public string Name { get; set; }
    public string Address { get; set; }
    public string Phone { get; set; }
    public double Longitude { get; set; }
    public double Latitude { get; set; }
    public int Distance { get; set; }
    public string Rating { get; set; }
}

#endregion

#region 高德地图API响应模型

/// <summary>
/// 地理编码响应
/// </summary>
public class GeocodeResponse
{
    public string status { get; set; }
    public Geocode[] geocodes { get; set; }
}

public class Geocode
{
    public string formatted_address { get; set; }
    public string location { get; set; }
    public string district { get; set; }
    public string city { get; set; }
    public string province { get; set; }
}

/// <summary>
/// 逆地理编码响应
/// </summary>
public class ReverseGeocodeResponse
{
    public string status { get; set; }
    public Regeocode regeocode { get; set; }
}

public class Regeocode
{
    public string formatted_address { get; set; }
    public AddressComponent addressComponent { get; set; }
}

public class AddressComponent
{
    public string district { get; set; }
    public string city { get; set; }
    public string province { get; set; }
}

/// <summary>
/// 路线规划响应
/// </summary>
public class RouteResponse
{
    public string status { get; set; }
    public Route route { get; set; }
}

public class Route
{
    public Path[] paths { get; set; }
}

public class Path
{
    public string distance { get; set; }
    public string duration { get; set; }
    public string tolls { get; set; }
    public string traffic_lights { get; set; }
    public Step[] steps { get; set; }
}

public class Step
{
    public string instruction { get; set; }
    public string distance { get; set; }
    public string duration { get; set; }
    public string road { get; set; }
}

/// <summary>
/// 地点搜索响应
/// </summary>
public class PlaceSearchResponse
{
    public string status { get; set; }
    public Poi[] pois { get; set; }
}

public class Poi
{
    public string name { get; set; }
    public string address { get; set; }
    public string tel { get; set; }
    public string location { get; set; }
    public string distance { get; set; }
    public BizExt biz_ext { get; set; }
}

public class BizExt
{
    public string rating { get; set; }
}

#endregion
