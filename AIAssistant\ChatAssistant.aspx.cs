using System;
using System.Web.UI;
using System.Web.Services;
using System.Threading.Tasks;
using Newtonsoft.Json;

public partial class AIAssistant_ChatAssistant : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户登录
        if (!User.Identity.IsAuthenticated || Session["UserID"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }
    }

    /// <summary>
    /// 处理用户消息的WebMethod
    /// </summary>
    /// <param name="message">用户消息</param>
    /// <param name="userLocation">用户位置（经度,纬度）</param>
    /// <returns>AI助手响应</returns>
    [WebMethod]
    public static async Task<string> ProcessMessage(string message, string userLocation)
    {
        try
        {
            // 验证用户登录状态
            if (System.Web.HttpContext.Current.Session["UserID"] == null)
            {
                return JsonConvert.SerializeObject(new
                {
                    Message = "请先登录后再使用智能助手。",
                    Intent = "error",
                    HasLocationData = false
                });
            }

            int userID = Convert.ToInt32(System.Web.HttpContext.Current.Session["UserID"]);

            // 处理用户消息
            var response = await ChatAssistantManager.ProcessUserMessageAsync(message, userID, userLocation);

            // 记录聊天历史（可选）
            await LogChatHistoryAsync(userID, message, response.Message, response.Intent);

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"处理消息时出错: {ex.Message}");
            return JsonConvert.SerializeObject(new
            {
                Message = "抱歉，处理您的请求时出现了问题，请稍后再试。",
                Intent = "error",
                HasLocationData = false
            });
        }
    }

    /// <summary>
    /// 获取维修店详细信息
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>维修店详细信息</returns>
    [WebMethod]
    public static string GetShopDetails(int shopID)
    {
        try
        {
            var shopData = ShopManager.GetShopWithLocation(shopID);
            if (shopData.Rows.Count == 0)
            {
                return JsonConvert.SerializeObject(new { success = false, message = "维修店不存在" });
            }

            var shop = shopData.Rows[0];
            var shopInfo = new
            {
                success = true,
                shop = new
                {
                    ShopID = Convert.ToInt32(shop["ShopID"]),
                    ShopName = shop["ShopName"].ToString(),
                    Address = shop["Address"].ToString(),
                    Description = shop["Description"]?.ToString(),
                    BusinessHours = shop["BusinessHours"]?.ToString(),
                    ContactPerson = shop["ContactPerson"]?.ToString(),
                    Phone = shop["Phone"]?.ToString(),
                    Rating = Convert.ToDouble(shop["Rating"]),
                    ReviewCount = Convert.ToInt32(shop["ReviewCount"]),
                    Longitude = shop["Longitude"] != DBNull.Value ? Convert.ToDouble(shop["Longitude"]) : 0,
                    Latitude = shop["Latitude"] != DBNull.Value ? Convert.ToDouble(shop["Latitude"]) : 0
                }
            };

            return JsonConvert.SerializeObject(shopInfo);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取维修店详情时出错: {ex.Message}");
            return JsonConvert.SerializeObject(new { success = false, message = "获取维修店信息失败" });
        }
    }

    /// <summary>
    /// 获取路线规划
    /// </summary>
    /// <param name="origin">起点（经度,纬度）</param>
    /// <param name="destination">终点（经度,纬度）</param>
    /// <param name="strategy">路线策略</param>
    /// <returns>路线信息</returns>
    [WebMethod]
    public static async Task<string> GetRoute(string origin, string destination, int strategy = 0)
    {
        try
        {
            var route = await AmapService.GetRouteAsync(origin, destination, strategy);
            if (route == null)
            {
                return JsonConvert.SerializeObject(new { success = false, message = "无法规划路线" });
            }

            return JsonConvert.SerializeObject(new { success = true, route = route });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路线规划时出错: {ex.Message}");
            return JsonConvert.SerializeObject(new { success = false, message = "路线规划失败" });
        }
    }

    /// <summary>
    /// 搜索附近维修店
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（公里）</param>
    /// <returns>附近维修店列表</returns>
    [WebMethod]
    public static async Task<string> SearchNearbyShops(double longitude, double latitude, double radius = 10)
    {
        try
        {
            // 从数据库获取附近维修店
            var dbShops = ShopManager.GetNearbyShops(longitude, latitude, radius);
            var shops = new System.Collections.Generic.List<object>();

            foreach (System.Data.DataRow shop in dbShops.Rows)
            {
                if (shop["Longitude"] != DBNull.Value && shop["Latitude"] != DBNull.Value)
                {
                    shops.Add(new
                    {
                        ShopID = Convert.ToInt32(shop["ShopID"]),
                        Name = shop["ShopName"].ToString(),
                        Address = shop["Address"].ToString(),
                        Rating = Convert.ToDouble(shop["Rating"]),
                        Distance = Convert.ToInt32(shop["Distance"]),
                        BusinessHours = shop["BusinessHours"]?.ToString(),
                        Longitude = Convert.ToDouble(shop["Longitude"]),
                        Latitude = Convert.ToDouble(shop["Latitude"])
                    });
                }
            }

            // 也可以从高德地图API获取更多维修店
            var amapShops = await AmapService.SearchNearbyShopsAsync(longitude, latitude, (int)(radius * 1000));
            foreach (var amapShop in amapShops)
            {
                shops.Add(new
                {
                    ShopID = 0, // 外部维修店没有内部ID
                    Name = amapShop.Name,
                    Address = amapShop.Address,
                    Rating = double.TryParse(amapShop.Rating, out double rating) ? rating : 0,
                    Distance = amapShop.Distance,
                    BusinessHours = "",
                    Longitude = amapShop.Longitude,
                    Latitude = amapShop.Latitude,
                    Phone = amapShop.Phone,
                    IsExternal = true
                });
            }

            return JsonConvert.SerializeObject(new { success = true, shops = shops });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"搜索附近维修店时出错: {ex.Message}");
            return JsonConvert.SerializeObject(new { success = false, message = "搜索失败" });
        }
    }

    /// <summary>
    /// 记录聊天历史
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="userMessage">用户消息</param>
    /// <param name="assistantResponse">助手响应</param>
    /// <param name="intent">意图</param>
    /// <returns>是否成功</returns>
    private static async Task<bool> LogChatHistoryAsync(int userID, string userMessage, string assistantResponse, string intent)
    {
        try
        {
            // 这里可以实现聊天历史记录功能
            // 例如保存到数据库中，用于后续分析和改进
            
            string query = @"
                INSERT INTO ChatHistory (UserID, UserMessage, AssistantResponse, Intent, ChatDate)
                VALUES (@UserID, @UserMessage, @AssistantResponse, @Intent, GETDATE())";

            var parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@UserID", userID),
                new System.Data.SqlClient.SqlParameter("@UserMessage", userMessage),
                new System.Data.SqlClient.SqlParameter("@AssistantResponse", assistantResponse),
                new System.Data.SqlClient.SqlParameter("@Intent", intent ?? "unknown")
            };

            // 注意：需要先创建ChatHistory表
            // int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            // return result > 0;
            
            return true; // 暂时返回true
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"记录聊天历史时出错: {ex.Message}");
            return false;
        }
    }
}
