using System;
using System.Data;
using System.Data.SqlClient;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections.Generic;
using System.Text;

public partial class CarOwner_ServiceSearch : System.Web.UI.Page
{
    private int userID;
    private int ownerID;
    private Dictionary<string, string> activeFilters;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 初始化用户ID和车主ID
        if (User.Identity.IsAuthenticated && Session["UserID"] != null)
        {
            userID = Convert.ToInt32(Session["UserID"]);
            // 获取车主ID
            string query = "SELECT OwnerID FROM CarOwners WHERE UserID = @UserID";
            SqlParameter[] parameters = { new SqlParameter("@UserID", userID) };
            DataTable dt = DatabaseHelper.ExecuteQuery(query, parameters);
            
            if (dt.Rows.Count > 0)
            {
                ownerID = Convert.ToInt32(dt.Rows[0]["OwnerID"]);
            }
        }
        
        if (!IsPostBack)
        {
            LoadServiceCategories();
            LoadServices();
        }
    }

    /// <summary>
    /// 加载服务类别
    /// </summary>
    private void LoadServiceCategories()
    {
        try
        {
            DataTable categories = ShopManager.GetServiceCategories();
            ddlServiceCategory.DataSource = categories;
            ddlServiceCategory.DataTextField = "CategoryName";
            ddlServiceCategory.DataValueField = "CategoryID";
            ddlServiceCategory.DataBind();
            
            // 添加"全部"选项
            ddlServiceCategory.Items.Insert(0, new ListItem("全部服务类型", "0"));
        }
        catch (Exception ex)
        {
            ShowError("加载服务类别时出错: " + ex.Message);
        }
    }

    /// <summary>
    /// 服务类别选择变更事件
    /// </summary>
    protected void ddlServiceCategory_SelectedIndexChanged(object sender, EventArgs e)
    {
        SearchServices();
    }

    /// <summary>
    /// 排序方式变更事件
    /// </summary>
    protected void ddlSortOrder_SelectedIndexChanged(object sender, EventArgs e)
    {
        SearchServices();
    }

    /// <summary>
    /// 应用筛选按钮点击事件
    /// </summary>
    protected void btnFilter_Click(object sender, EventArgs e)
    {
        SearchServices();
    }

    /// <summary>
    /// 重置筛选按钮点击事件
    /// </summary>
    protected void btnReset_Click(object sender, EventArgs e)
    {
        // 重置所有筛选条件
        ddlServiceCategory.SelectedIndex = 0;
        txtMinPrice.Text = string.Empty;
        txtMaxPrice.Text = string.Empty;
        txtLocation.Text = string.Empty;
        rblRating.SelectedValue = "0";
        
        // 重置活动过滤器
        activeFilters = new Dictionary<string, string>();
        ViewState["ActiveFilters"] = activeFilters;
        
        // 重新搜索
        SearchServices();
    }

    /// <summary>
    /// 服务搜索方法
    /// </summary>
    private void SearchServices()
    {
        try
        {
            // 获取筛选参数
            int categoryID = Convert.ToInt32(ddlServiceCategory.SelectedValue);
            
            decimal? minPrice = null;
            if (!string.IsNullOrEmpty(txtMinPrice.Text) && decimal.TryParse(txtMinPrice.Text, out decimal min))
            {
                minPrice = min;
            }
            
            decimal? maxPrice = null;
            if (!string.IsNullOrEmpty(txtMaxPrice.Text) && decimal.TryParse(txtMaxPrice.Text, out decimal max))
            {
                maxPrice = max;
            }
            
            string location = txtLocation.Text.Trim();
            
            int rating = Convert.ToInt32(rblRating.SelectedValue);
            
            string sortOrder = ddlSortOrder.SelectedValue;
            
            // 创建活动过滤器字典
            activeFilters = new Dictionary<string, string>();
            
            // 添加活动的筛选条件
            if (categoryID > 0)
            {
                activeFilters.Add("category", ddlServiceCategory.SelectedItem.Text);
            }
            
            if (minPrice.HasValue || maxPrice.HasValue)
            {
                string priceFilter = "";
                if (minPrice.HasValue && maxPrice.HasValue)
                {
                    priceFilter = $"¥{minPrice.Value} - ¥{maxPrice.Value}";
                }
                else if (minPrice.HasValue)
                {
                    priceFilter = $"¥{minPrice.Value} 以上";
                }
                else if (maxPrice.HasValue)
                {
                    priceFilter = $"¥{maxPrice.Value} 以下";
                }
                activeFilters.Add("price", priceFilter);
            }
            
            if (!string.IsNullOrEmpty(location))
            {
                activeFilters.Add("location", location);
            }
            
            if (rating > 0)
            {
                activeFilters.Add("rating", $"{rating}星及以上");
            }
            
            // 保存活动过滤器到ViewState
            ViewState["ActiveFilters"] = activeFilters;
            
            // 更新活动过滤器UI
            UpdateActiveFilters();
            
            // 执行搜索查询
            DataTable services = ServiceSearch(categoryID, minPrice, maxPrice, location, rating, sortOrder);
            
            // 绑定数据到ListView
            lvServices.DataSource = services;
            lvServices.DataBind();
            
            // 更新结果计数
            lblResultCount.Text = $"找到 {services.Rows.Count} 个服务";
        }
        catch (Exception ex)
        {
            ShowError("搜索服务时出错: " + ex.Message);
        }
    }

    /// <summary>
    /// 更新活动过滤器UI
    /// </summary>
    private void UpdateActiveFilters()
    {
        if (activeFilters != null && activeFilters.Count > 0)
        {
            pnlActiveFilters.Visible = true;
            StringBuilder sb = new StringBuilder();
            
            foreach (var filter in activeFilters)
            {
                string filterName = "";
                switch (filter.Key)
                {
                    case "category":
                        filterName = "服务类型";
                        break;
                    case "price":
                        filterName = "价格";
                        break;
                    case "location":
                        filterName = "地址";
                        break;
                    case "rating":
                        filterName = "评分";
                        break;
                }
                
                sb.AppendFormat("<span class='filter-badge'>{0}: {1}</span>", filterName, HttpUtility.HtmlEncode(filter.Value));
            }
            
            phActiveFilters.Controls.Add(new LiteralControl(sb.ToString()));
        }
        else
        {
            pnlActiveFilters.Visible = false;
            phActiveFilters.Controls.Clear();
        }
    }
    
    /// <summary>
    /// 搜索服务的数据库查询
    /// </summary>
    private DataTable ServiceSearch(int categoryID, decimal? minPrice, decimal? maxPrice, string location, int rating, string sortOrder)
    {
        try
        {
            // 构建SQL查询
            string query = @"SELECT 
                             rs.ServiceID, 
                             rs.ServiceName, 
                             rs.Description, 
                             rs.EstimatedTime, 
                             rs.BasePrice, 
                             rs.ShopID,
                             sc.CategoryID,
                             sc.CategoryName,
                             shop.ShopName, 
                             shop.Address,
                             COALESCE(
                                (SELECT AVG(CAST(r.Rating AS DECIMAL(3,1)))
                                 FROM Reviews r
                                 WHERE r.ShopID = shop.ShopID
                                 GROUP BY r.ShopID),
                                0
                             ) as AvgRating
                             FROM RepairServices rs
                             INNER JOIN RepairShops shop ON rs.ShopID = shop.ShopID
                             INNER JOIN ServiceCategories sc ON rs.CategoryID = sc.CategoryID
                             WHERE rs.IsActive = 1";
            
            // 添加筛选条件
            List<System.Data.SqlClient.SqlParameter> parameters = new List<System.Data.SqlClient.SqlParameter>();
            
            // 类别筛选
            if (categoryID > 0)
            {
                query += " AND rs.CategoryID = @CategoryID";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@CategoryID", categoryID));
            }
            
            // 价格范围筛选
            if (minPrice.HasValue)
            {
                query += " AND rs.BasePrice >= @MinPrice";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@MinPrice", minPrice.Value));
            }
            
            if (maxPrice.HasValue)
            {
                query += " AND rs.BasePrice <= @MaxPrice";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@MaxPrice", maxPrice.Value));
            }
            
            // 地址筛选
            if (!string.IsNullOrEmpty(location))
            {
                query += " AND shop.Address LIKE @Location";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@Location", "%" + location + "%"));
            }
            
            // 评分筛选
            if (rating > 0)
            {
                query += " AND (SELECT AVG(CAST(r.Rating AS DECIMAL(3,1))) FROM Reviews r WHERE r.ShopID = shop.ShopID GROUP BY r.ShopID) >= @Rating";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@Rating", rating));
            }
            
            // 排序
            switch (sortOrder)
            {
                case "price_asc":
                    query += " ORDER BY rs.BasePrice ASC";
                    break;
                case "price_desc":
                    query += " ORDER BY rs.BasePrice DESC";
                    break;
                case "rating_desc":
                default:
                    query += " ORDER BY AvgRating DESC, rs.BasePrice ASC";
                    break;
            }
            
            // 执行查询
            return DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
        }
        catch (Exception ex)
        {
            throw new Exception("执行服务搜索查询时出错: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 生成星级评分显示
    /// </summary>
    protected string GetStarRating(decimal rating)
    {
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i <= 5; i++)
        {
            if (i <= rating)
            {
                // 实心星
                sb.Append("<i class=\"fas fa-star\"></i>");
            }
            else if ((decimal)(i - 0.5m) <= rating)
            {
                // 半星
                sb.Append("<i class=\"fas fa-star-half-alt\"></i>");
            }
            else
            {
                // 空星
                sb.Append("<i class=\"far fa-star\"></i>");
            }
        }
        return sb.ToString();
    }
    
    /// <summary>
    /// ListView项命令事件处理
    /// </summary>
    protected void lbtnBook_Click(object sender, EventArgs e)
    {
        try
        {
            LinkButton btn = (LinkButton)sender;
            string commandArgument = btn.CommandArgument;
            
            // 检查用户是否登录
            if (!User.Identity.IsAuthenticated)
            {
                Response.Redirect("~/Login.aspx?returnurl=" + Server.UrlEncode(Request.RawUrl));
                return;
            }

            // 解析命令参数
            string[] args = commandArgument.Split(',');
            
            if (args.Length != 2)
            {
                ShowError("参数错误");
                return;
            }

            int serviceID = Convert.ToInt32(args[0]);
            int shopID = Convert.ToInt32(args[1]);

            // 设置隐藏字段
            hfServiceID.Value = serviceID.ToString();
            hfShopID.Value = shopID.ToString();

            // 加载服务信息
            LoadServiceInfoForBooking(serviceID, shopID);

            // 加载用户车辆
            LoadUserCarsForBooking();

            // 设置默认预约时间
            DateTime minTime = DateTime.Now.AddHours(1);
            txtModalAppointmentDate.Text = minTime.ToString("yyyy-MM-ddTHH:mm");
            txtModalAppointmentDate.Attributes["min"] = minTime.ToString("yyyy-MM-ddTHH:mm");

            // 清空描述
            txtModalDescription.Text = "";

            // 显示模态框
            ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
        }
        catch (Exception ex)
        {
            ShowError("处理预约请求时发生错误: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 获取服务信息
    /// </summary>
    private DataTable GetServiceInfo(int serviceID, int shopID)
    {
        string query = @"SELECT rs.ServiceID, rs.ServiceName, rs.Description, rs.EstimatedTime, 
                        rs.BasePrice, shop.ShopID, shop.ShopName, shop.Address
                        FROM RepairServices rs
                        INNER JOIN RepairShops shop ON rs.ShopID = shop.ShopID
                        WHERE rs.ServiceID = @ServiceID AND shop.ShopID = @ShopID";
        
        System.Data.SqlClient.SqlParameter[] parameters = {
            new System.Data.SqlClient.SqlParameter("@ServiceID", serviceID),
            new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
        };
        
        return DatabaseHelper.ExecuteQuery(query, parameters);
    }
    
    /// <summary>
    /// 加载车辆下拉列表
    /// </summary>
    private void LoadCars()
    {
        try
        {
            DataTable cars = CarManager.GetCarsByOwnerID(ownerID);
            
            // 添加计算列CarInfo
            if (!cars.Columns.Contains("CarInfo"))
            {
                cars.Columns.Add("CarInfo", typeof(string), "Make + ' ' + Model + ' (' + LicensePlate + ')'");
            }
            
            ddlModalCar.DataSource = cars;
            ddlModalCar.DataTextField = "CarInfo";
            ddlModalCar.DataValueField = "CarID";
            ddlModalCar.DataBind();
            
            // 添加默认选项
            ddlModalCar.Items.Insert(0, new ListItem("-- 请选择车辆 --", "0"));
        }
        catch (Exception ex)
        {
            ShowError("加载车辆列表时出错: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 预约时间验证
    /// </summary>
    protected void cvModalAppointmentDate_ServerValidate(object source, ServerValidateEventArgs args)
    {
        if (string.IsNullOrEmpty(args.Value))
        {
            args.IsValid = false;
            return;
        }

        DateTime appointmentDate;
        if (DateTime.TryParse(args.Value, out appointmentDate))
        {
            if (appointmentDate <= DateTime.Now)
            {
                args.IsValid = false;
            }
            else
            {
                args.IsValid = true;
            }
        }
        else
        {
            args.IsValid = false;
        }
    }
    
    /// <summary>
    /// 提交预约按钮点击事件
    /// </summary>
    protected void btnModalSubmit_Click(object sender, EventArgs e)
    {
        try
        {
            // 检查用户登录状态
            if (!User.Identity.IsAuthenticated || Session["UserID"] == null)
            {
                ShowModalError("请先登录后再预约服务");
                return;
            }

            int userID = Convert.ToInt32(Session["UserID"]);

            // 检查隐藏字段值
            
            if (string.IsNullOrEmpty(hfServiceID.Value) || string.IsNullOrEmpty(hfShopID.Value))
            {
                ShowModalError("服务信息丢失，请重新选择服务");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
                return;
            }

            // 获取表单数据
            int serviceID = Convert.ToInt32(hfServiceID.Value);
            int shopID = Convert.ToInt32(hfShopID.Value);

            // 验证车辆选择
            
            if (ddlModalCar.SelectedIndex <= 0 || ddlModalCar.SelectedValue == "0")
            {
                ShowModalError("请选择车辆");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
                return;
            }
            int carID = Convert.ToInt32(ddlModalCar.SelectedValue);

            // 验证预约时间
            
            if (string.IsNullOrEmpty(txtModalAppointmentDate.Text))
            {
                ShowModalError("请选择预约时间");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
                return;
            }

            DateTime appointmentDate;
            if (!DateTime.TryParse(txtModalAppointmentDate.Text, out appointmentDate))
            {
                ShowModalError("预约时间格式无效");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
                return;
            }

            if (appointmentDate <= DateTime.Now)
            {
                ShowModalError("预约时间必须是将来的时间");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
                return;
            }

            string description = txtModalDescription.Text.Trim();

            // 调用创建预约方法前的日志
            

            // 创建预约
            int appointmentID = AppointmentManager.AddAppointment(carID, shopID, serviceID, appointmentDate, description);
            

            if (appointmentID > 0)
            {
                
                
                // 预约成功，设置会话变量
                Session["AppointmentSuccess"] = "预约成功创建！维修店将尽快处理您的预约请求。";
                
                // 清除模态框相关的隐藏字段
                hfServiceID.Value = "";
                hfShopID.Value = "";
                
                
                
                // 使用服务器端重定向
                Response.Redirect($"~/CarOwner/Appointments.aspx?id={appointmentID}", false);
                Context.ApplicationInstance.CompleteRequest();
            }
            else
            {
                
                ShowModalError("创建预约时发生错误，请稍后再试");
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
            }
        }
        catch (Exception ex)
        {
            
            ShowModalError("创建预约时发生错误：" + ex.Message);
            ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showBookingModal();", true);
        }
    }
    
    /// <summary>
    /// 显示消息
    /// </summary>
    private void ShowMessage(string message, string type)
    {
        pnlMessage.Visible = true;
        switch (type.ToLower())
        {
            case "success":
                pnlMessage.CssClass = "alert alert-success mb-4";
                litMessage.Text = $"<i class=\"fas fa-check-circle\"></i> {message}";
                break;
            case "danger":
            case "error":
                pnlMessage.CssClass = "alert alert-danger mb-4";
                litMessage.Text = $"<i class=\"fas fa-exclamation-circle\"></i> {message}";
                break;
            case "warning":
                pnlMessage.CssClass = "alert alert-warning mb-4";
                litMessage.Text = $"<i class=\"fas fa-exclamation-triangle\"></i> {message}";
                break;
            default:
                pnlMessage.CssClass = "alert alert-info mb-4";
                litMessage.Text = $"<i class=\"fas fa-info-circle\"></i> {message}";
                break;
        }
    }
    
    /// <summary>
    /// 显示模态框错误消息
    /// </summary>
    private void ShowModalError(string message)
    {
        modalErrorMessage.Visible = true;
        litModalError.Text = $"<i class=\"fas fa-exclamation-circle\"></i> {message}";
    }
    
    /// <summary>
    /// 显示错误消息
    /// </summary>
    private void ShowError(string message)
    {
        ShowMessage(message, "error");
    }
    
    /// <summary>
    /// 为预约加载服务信息
    /// </summary>
    private void LoadServiceInfoForBooking(int serviceID, int shopID)
    {
        try
        {
            DataTable serviceInfo = GetServiceInfo(serviceID, shopID);
            if (serviceInfo.Rows.Count > 0)
            {
                DataRow row = serviceInfo.Rows[0];
                string serviceInfoHtml = $@"
                    <div class='mb-3'>
                        <h6><strong>服务名称：</strong>{row["ServiceName"]}</h6>
                        <p><strong>维修店：</strong>{row["ShopName"]}</p>
                        <p><strong>地址：</strong>{row["Address"]}</p>
                        <p><strong>预计时间：</strong>{row["EstimatedTime"]} 分钟</p>
                        <p><strong>基础价格：</strong>¥{Convert.ToDecimal(row["BasePrice"]):F2}</p>
                        <p><strong>服务描述：</strong>{row["Description"]}</p>
                    </div>";
                
                lblModalServiceInfo.Text = serviceInfoHtml;
            }
        }
        catch (Exception)
        {
            lblModalServiceInfo.Text = "<div class='alert alert-warning'>无法加载服务信息</div>";
        }
    }
    
    /// <summary>
    /// 为预约加载用户车辆
    /// </summary>
    private void LoadUserCarsForBooking()
    {
        try
        {
            // 检查用户是否登录且ownerID是否有效
            if (!User.Identity.IsAuthenticated || Session["UserID"] == null)
            {
                ddlModalCar.Items.Clear();
                ddlModalCar.Items.Add(new ListItem("请先登录", "0"));
                return;
            }

            if (ownerID <= 0)
            {
                // 尝试重新获取ownerID
                int currentUserID = Convert.ToInt32(Session["UserID"]);
                string query = "SELECT OwnerID FROM CarOwners WHERE UserID = @UserID";
                SqlParameter[] parameters = { new SqlParameter("@UserID", currentUserID) };
                DataTable dt = DatabaseHelper.ExecuteQuery(query, parameters);
                
                if (dt.Rows.Count > 0)
                {
                    ownerID = Convert.ToInt32(dt.Rows[0]["OwnerID"]);
                }
                else
                {
                    ddlModalCar.Items.Clear();
                    ddlModalCar.Items.Add(new ListItem("未找到车主信息", "0"));
                    return;
                }
            }

            // 获取车辆信息
            DataTable cars = CarManager.GetCarsByOwnerID(ownerID);
            
            if (cars == null || cars.Rows.Count == 0)
            {
                ddlModalCar.Items.Clear();
                ddlModalCar.Items.Add(new ListItem("您还没有添加车辆", "0"));
                return;
            }
            
            // 添加计算列CarInfo（如果不存在）
            if (!cars.Columns.Contains("CarInfo"))
            {
                cars.Columns.Add("CarInfo", typeof(string), "Make + ' ' + Model + ' (' + LicensePlate + ')'");
            }
            
            ddlModalCar.DataSource = cars;
            ddlModalCar.DataTextField = "CarInfo";
            ddlModalCar.DataValueField = "CarID";
            ddlModalCar.DataBind();
            
            // 添加默认选项
            ddlModalCar.Items.Insert(0, new ListItem("-- 请选择车辆 --", "0"));
            
        }
        catch (Exception)
        {
            ddlModalCar.Items.Clear();
            ddlModalCar.Items.Add(new ListItem("加载车辆失败", "0"));
    }
}

/// <summary>
/// 加载所有服务
/// </summary>
private void LoadServices()
{
    try
    {
        DataTable services = ServiceSearch(0, null, null, "", 0, "rating_desc");
        lvServices.DataSource = services;
        lvServices.DataBind();
        lblResultCount.Text = $"找到 {services.Rows.Count} 个服务";
    }
    catch (Exception ex)
    {
        ShowError("加载服务时出错: " + ex.Message);
        }
    }
}













