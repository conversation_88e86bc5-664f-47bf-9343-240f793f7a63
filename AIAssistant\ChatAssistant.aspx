<%@ Page Title="智能聊天助手" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ChatAssistant.aspx.cs" Inherits="AIAssistant_ChatAssistant" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <style>
        .chat-assistant-container {
            height: 80vh;
            display: flex;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }
        
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 400px;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h3 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .chat-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e0e0e0;
            margin-left: 10px;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
            margin-right: 10px;
        }
        
        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .message.assistant .message-avatar {
            background: #28a745;
        }
        
        .message.user .message-avatar {
            background: #667eea;
        }
        
        .chat-input-area {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .send-btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .send-btn:hover {
            background: #5a6fd8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .map-panel {
            width: 400px;
            border-left: 1px solid #ddd;
            display: none;
        }
        
        .map-panel.show {
            display: block;
        }
        
        .map-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        
        .map-container {
            height: calc(100% - 60px);
            position: relative;
        }
        
        .shop-list {
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
        }
        
        .shop-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .shop-item:hover {
            background: #f0f0f0;
        }
        
        .shop-name {
            font-weight: bold;
            color: #333;
        }
        
        .shop-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #666;
        }
        
        .typing-indicator.show {
            display: block;
        }
        
        .quick-actions {
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
        }
        
        .quick-action-btn {
            display: inline-block;
            padding: 6px 12px;
            margin: 2px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .quick-action-btn:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        
        .location-prompt {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .route-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .route-info h5 {
            margin: 0 0 10px 0;
            color: #155724;
        }
        
        .route-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 12px;
        }
    </style>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=1.4.15&key=95a8d6c364456acd37faf8d0d8cfbc04&plugin=AMap.Geocoder"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container-fluid mt-4">
        <div class="chat-assistant-container">
            <!-- 聊天面板 -->
            <div class="chat-panel">
                <!-- 聊天头部 -->
                <div class="chat-header">
                    <h3>🤖 智能维修助手</h3>
                    <p>我可以帮您找到最近的维修店并规划最佳路线</p>
                </div>
                
                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <span style="font-size: 12px; color: #666;">快捷操作：</span>
                    <span class="quick-action-btn" onclick="sendQuickMessage('我想找最近的维修店')">🔍 找维修店</span>
                    <span class="quick-action-btn" onclick="sendQuickMessage('我的车发动机有异响')">🔧 故障诊断</span>
                    <span class="quick-action-btn" onclick="sendQuickMessage('保养需要多少钱')">💰 价格咨询</span>
                    <span class="quick-action-btn" onclick="sendQuickMessage('什么时候需要保养')">📅 保养建议</span>
                </div>
                
                <!-- 聊天消息区域 -->
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            您好！我是您的智能维修助手。我可以帮您：<br/>
                            • 🗺️ 找到附近的维修店并规划路线<br/>
                            • 🔧 诊断汽车故障问题<br/>
                            • 💰 提供维修价格估算<br/>
                            • 📅 给出保养建议<br/><br/>
                            请告诉我您需要什么帮助？
                        </div>
                    </div>
                </div>
                
                <!-- 输入中指示器 -->
                <div class="typing-indicator" id="typingIndicator">
                    🤖 助手正在思考中...
                </div>
                
                <!-- 聊天输入区域 -->
                <div class="chat-input-area">
                    <div class="input-group">
                        <input type="text" class="chat-input" id="messageInput" placeholder="输入您的问题..." 
                               onkeypress="handleEnterKey(event)" />
                        <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
            
            <!-- 地图面板 -->
            <div class="map-panel" id="mapPanel">
                <div class="map-header">
                    📍 位置和路线信息
                </div>
                <div class="map-container">
                    <div id="mapContainer" style="height: 60%; width: 100%;"></div>
                    <div class="shop-list" id="shopList">
                        <!-- 维修店列表将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var map = null;
        var userLocation = null;
        var currentMarkers = [];

        // 页面加载完成后初始化
        $(document).ready(function() {
            initializeMap();
            requestUserLocation();
        });

        // 初始化地图
        function initializeMap() {
            map = new AMap.Map('mapContainer', {
                zoom: 13,
                center: [116.397428, 39.90923] // 默认北京
            });
        }

        // 请求用户位置
        function requestUserLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        userLocation = {
                            longitude: position.coords.longitude,
                            latitude: position.coords.latitude
                        };
                        
                        // 更新地图中心
                        map.setCenter([userLocation.longitude, userLocation.latitude]);
                        
                        // 添加用户位置标记
                        var userMarker = new AMap.Marker({
                            position: [userLocation.longitude, userLocation.latitude],
                            icon: new AMap.Icon({
                                image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                                size: new AMap.Size(19, 33)
                            }),
                            title: '您的位置'
                        });
                        map.add(userMarker);
                    },
                    function(error) {
                        console.log('获取位置失败:', error);
                        addMessage('assistant', '无法获取您的位置信息，您可以手动输入地址。');
                    }
                );
            }
        }

        // 处理回车键
        function handleEnterKey(event) {
            if (event.keyCode === 13) {
                sendMessage();
            }
        }

        // 发送快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 发送消息
        function sendMessage() {
            var input = document.getElementById('messageInput');
            var message = input.value.trim();
            
            if (message === '') return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 显示输入中指示器
            showTypingIndicator();
            
            // 发送到服务器
            $.ajax({
                type: "POST",
                url: "ChatAssistant.aspx/ProcessMessage",
                data: JSON.stringify({ 
                    message: message,
                    userLocation: userLocation ? `${userLocation.longitude},${userLocation.latitude}` : null
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    hideTypingIndicator();
                    handleAssistantResponse(response.d);
                },
                error: function(xhr, status, error) {
                    hideTypingIndicator();
                    addMessage('assistant', '抱歉，服务暂时不可用，请稍后再试。');
                }
            });
        }

        // 处理助手响应
        function handleAssistantResponse(responseData) {
            try {
                var response = JSON.parse(responseData);
                
                // 显示助手消息
                addMessage('assistant', response.Message);
                
                // 如果有位置数据，显示地图
                if (response.HasLocationData) {
                    showMapPanel();
                    
                    if (response.RecommendedShops && response.RecommendedShops.length > 0) {
                        displayShopsOnMap(response.RecommendedShops);
                        displayShopsList(response.RecommendedShops);
                    }
                    
                    if (response.RouteInfo) {
                        displayRouteInfo(response.RouteInfo);
                    }
                }
            } catch (e) {
                addMessage('assistant', responseData);
            }
        }

        // 添加消息到聊天界面
        function addMessage(sender, content) {
            var messagesContainer = document.getElementById('chatMessages');
            var messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + sender;
            
            var avatar = sender === 'user' ? '👤' : '🤖';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${content}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示/隐藏输入中指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').classList.add('show');
            document.getElementById('sendBtn').disabled = true;
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').classList.remove('show');
            document.getElementById('sendBtn').disabled = false;
        }

        // 显示地图面板
        function showMapPanel() {
            document.getElementById('mapPanel').classList.add('show');
            setTimeout(() => map.getViewport().resize(), 100);
        }

        // 在地图上显示维修店
        function displayShopsOnMap(shops) {
            // 清除之前的标记
            map.remove(currentMarkers);
            currentMarkers = [];
            
            shops.forEach(function(shop, index) {
                if (shop.Longitude && shop.Latitude) {
                    var marker = new AMap.Marker({
                        position: [shop.Longitude, shop.Latitude],
                        icon: new AMap.Icon({
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                            size: new AMap.Size(19, 33)
                        }),
                        title: shop.Name
                    });
                    
                    // 添加点击事件
                    marker.on('click', function() {
                        var infoWindow = new AMap.InfoWindow({
                            content: `
                                <div style="padding: 10px;">
                                    <h5>${shop.Name}</h5>
                                    <p>${shop.Address}</p>
                                    <p>评分: ${shop.Rating}分</p>
                                    ${shop.Distance ? `<p>距离: ${shop.Distance}米</p>` : ''}
                                </div>
                            `
                        });
                        infoWindow.open(map, marker.getPosition());
                    });
                    
                    map.add(marker);
                    currentMarkers.push(marker);
                }
            });
            
            // 调整地图视野
            if (currentMarkers.length > 0) {
                map.setFitView(currentMarkers);
            }
        }

        // 显示维修店列表
        function displayShopsList(shops) {
            var shopList = document.getElementById('shopList');
            shopList.innerHTML = '<h6 style="padding: 10px; margin: 0; background: #f8f9fa;">推荐维修店</h6>';
            
            shops.forEach(function(shop) {
                var shopItem = document.createElement('div');
                shopItem.className = 'shop-item';
                shopItem.innerHTML = `
                    <div class="shop-name">${shop.Name}</div>
                    <div class="shop-info">
                        📍 ${shop.Address}<br/>
                        ⭐ ${shop.Rating}分
                        ${shop.Distance ? ` | 📏 ${shop.Distance}米` : ''}
                        ${shop.BusinessHours ? ` | 🕐 ${shop.BusinessHours}` : ''}
                    </div>
                `;
                
                shopItem.onclick = function() {
                    if (shop.Longitude && shop.Latitude) {
                        map.setCenter([shop.Longitude, shop.Latitude]);
                        map.setZoom(16);
                    }
                };
                
                shopList.appendChild(shopItem);
            });
        }

        // 显示路线信息
        function displayRouteInfo(routeInfo) {
            var routeDiv = document.createElement('div');
            routeDiv.className = 'route-info';
            routeDiv.innerHTML = `
                <h5>🗺️ 路线信息</h5>
                <div class="route-details">
                    <div>📏 距离: ${(routeInfo.Distance / 1000).toFixed(1)}公里</div>
                    <div>🕐 时间: ${Math.round(routeInfo.Duration / 60)}分钟</div>
                    <div>💰 过路费: ${routeInfo.Tolls}元</div>
                    <div>🚦 红绿灯: ${routeInfo.TrafficLights}个</div>
                </div>
            `;
            
            var messagesContainer = document.getElementById('chatMessages');
            messagesContainer.appendChild(routeDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    </script>
</asp:Content>
