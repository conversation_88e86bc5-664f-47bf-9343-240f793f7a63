using System;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Configuration;
using System.Net;
using System.IO;

/// <summary>
/// AI服务类，用于与AI模型进行通信
/// </summary>
public class AIService
{
    private static readonly string AI_API_KEY = "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";
    private static readonly string AI_API_URL = "https://openrouter.ai/api/v1/chat/completions";

    /// <summary>
    /// 发送消息到AI模型并获取响应
    /// </summary>
    /// <param name="userMessage">用户消息</param>
    /// <param name="systemPrompt">系统提示</param>
    /// <param name="conversationHistory">对话历史</param>
    /// <returns>AI响应</returns>
    public static async Task<string> GetAIResponseAsync(string userMessage, string systemPrompt = null, string conversationHistory = null)
    {
        try
        {
            var messages = new System.Collections.Generic.List<object>();

            // 添加系统提示
            if (!string.IsNullOrEmpty(systemPrompt))
            {
                messages.Add(new { role = "system", content = systemPrompt });
            }

            // 添加对话历史（如果有）
            if (!string.IsNullOrEmpty(conversationHistory))
            {
                // 这里可以解析对话历史并添加到messages中
                // 暂时简化处理
            }

            // 添加用户消息
            messages.Add(new { role = "user", content = userMessage });

            var requestBody = new
            {
                model = "anthropic/claude-3.5-sonnet",
                messages = messages,
                max_tokens = 1000,
                temperature = 0.7
            };

            var json = JsonConvert.SerializeObject(requestBody);

            // 使用WebRequest替代HttpClient
            var request = (HttpWebRequest)WebRequest.Create(AI_API_URL);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Headers.Add("Authorization", $"Bearer {AI_API_KEY}");
            request.Headers.Add("HTTP-Referer", "https://localhost");
            request.Headers.Add("X-Title", "汽车维修服务平台");

            // 写入请求数据
            using (var streamWriter = new StreamWriter(await request.GetRequestStreamAsync()))
            {
                await streamWriter.WriteAsync(json);
            }

            // 获取响应
            using (var response = (HttpWebResponse)await request.GetResponseAsync())
            using (var streamReader = new StreamReader(response.GetResponseStream()))
            {
                var responseContent = await streamReader.ReadToEndAsync();

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    var aiResponse = JsonConvert.DeserializeObject<AIResponse>(responseContent);
                    return aiResponse?.choices?[0]?.message?.content ?? "抱歉，我暂时无法回答您的问题。";
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"AI API错误: {response.StatusCode} - {responseContent}");
                    return "抱歉，AI服务暂时不可用，请稍后再试。";
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"AI服务异常: {ex.Message}");
            return "抱歉，处理您的请求时出现了问题，请稍后再试。";
        }
    }

    /// <summary>
    /// 获取汽车维修相关的AI响应
    /// </summary>
    /// <param name="userMessage">用户消息</param>
    /// <param name="userLocation">用户位置</param>
    /// <param name="nearbyShops">附近维修店信息</param>
    /// <returns>AI响应</returns>
    public static async Task<string> GetCarRepairAdviceAsync(string userMessage, string userLocation = null, string nearbyShops = null)
    {
        var systemPrompt = @"你是一个专业的汽车维修智能助手，具有以下能力：

1. 汽车故障诊断和维修建议
2. 维修店推荐和路线规划
3. 维修价格估算
4. 保养建议

请遵循以下原则：
- 提供专业、准确的汽车维修建议
- 如果涉及路线规划，优先推荐距离近、评价好的维修店
- 考虑用户的具体情况（位置、车型、故障描述等）
- 回答要简洁明了，重点突出
- 如果需要更多信息才能给出准确建议，请主动询问

当前用户位置：" + (userLocation ?? "未知") + @"
附近维修店信息：" + (nearbyShops ?? "暂无") + @"

请根据用户的问题提供专业的建议。";

        return await GetAIResponseAsync(userMessage, systemPrompt);
    }

    /// <summary>
    /// 分析用户意图
    /// </summary>
    /// <param name="userMessage">用户消息</param>
    /// <returns>意图分析结果</returns>
    public static async Task<IntentAnalysisResult> AnalyzeIntentAsync(string userMessage)
    {
        var systemPrompt = @"请分析用户消息的意图，并返回JSON格式的结果。

可能的意图类型：
- route_planning: 路线规划
- shop_recommendation: 维修店推荐
- fault_diagnosis: 故障诊断
- price_inquiry: 价格咨询
- maintenance_advice: 保养建议
- general_chat: 一般聊天

返回格式：
{
  ""intent"": ""意图类型"",
  ""confidence"": 0.95,
  ""entities"": {
    ""location"": ""提取的位置信息"",
    ""car_model"": ""提取的车型信息"",
    ""problem"": ""提取的问题描述""
  }
}";

        try
        {
            var response = await GetAIResponseAsync(userMessage, systemPrompt);
            
            // 尝试解析JSON响应
            var result = JsonConvert.DeserializeObject<IntentAnalysisResult>(response);
            return result ?? new IntentAnalysisResult 
            { 
                Intent = "general_chat", 
                Confidence = 0.5,
                Entities = new System.Collections.Generic.Dictionary<string, string>()
            };
        }
        catch
        {
            // 如果解析失败，返回默认结果
            return new IntentAnalysisResult 
            { 
                Intent = "general_chat", 
                Confidence = 0.5,
                Entities = new System.Collections.Generic.Dictionary<string, string>()
            };
        }
    }
}

/// <summary>
/// AI响应数据结构
/// </summary>
public class AIResponse
{
    public Choice[] choices { get; set; }
}

public class Choice
{
    public Message message { get; set; }
}

public class Message
{
    public string content { get; set; }
}

/// <summary>
/// 意图分析结果
/// </summary>
public class IntentAnalysisResult
{
    [JsonProperty("intent")]
    public string Intent { get; set; }

    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    [JsonProperty("entities")]
    public System.Collections.Generic.Dictionary<string, string> Entities { get; set; }
}
