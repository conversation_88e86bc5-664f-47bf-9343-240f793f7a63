-- 创建聊天系统相关表

-- 1. 聊天室表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ChatRooms' AND xtype='U')
BEGIN
    CREATE TABLE ChatRooms (
        RoomID INT IDENTITY(1,1) PRIMARY KEY,
        RoomName NVARCHAR(100) NOT NULL,
        RoomType NVARCHAR(20) NOT NULL, -- 'Public' 公共群聊, 'Private' 私聊
        Description NVARCHAR(500),
        CreatedBy INT NOT NULL, -- 创建者用户ID
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        IsActive BIT NOT NULL DEFAULT 1,
        LastMessageDate DATETIME,
        FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
    );
    
    CREATE INDEX IX_ChatRooms_RoomType ON ChatRooms(RoomType);
    CREATE INDEX IX_ChatRooms_IsActive ON ChatRooms(IsActive);
    
    PRINT '聊天室表 ChatRooms 创建成功';
END
ELSE
BEGIN
    PRINT '聊天室表 ChatRooms 已存在';
END

-- 2. 聊天消息表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ChatMessages' AND xtype='U')
BEGIN
    CREATE TABLE ChatMessages (
        MessageID INT IDENTITY(1,1) PRIMARY KEY,
        RoomID INT NOT NULL,
        SenderID INT NOT NULL,
        MessageContent NVARCHAR(1000) NOT NULL,
        MessageType NVARCHAR(20) NOT NULL DEFAULT 'Text', -- 'Text', 'Image', 'File'
        SentDate DATETIME NOT NULL DEFAULT GETDATE(),
        IsDeleted BIT NOT NULL DEFAULT 0,
        DeletedBy INT NULL, -- 删除者用户ID（管理员）
        DeletedDate DATETIME NULL,
        FOREIGN KEY (RoomID) REFERENCES ChatRooms(RoomID) ON DELETE CASCADE,
        FOREIGN KEY (SenderID) REFERENCES Users(UserID),
        FOREIGN KEY (DeletedBy) REFERENCES Users(UserID)
    );
    
    CREATE INDEX IX_ChatMessages_RoomID ON ChatMessages(RoomID);
    CREATE INDEX IX_ChatMessages_SenderID ON ChatMessages(SenderID);
    CREATE INDEX IX_ChatMessages_SentDate ON ChatMessages(SentDate);
    CREATE INDEX IX_ChatMessages_IsDeleted ON ChatMessages(IsDeleted);
    
    PRINT '聊天消息表 ChatMessages 创建成功';
END
ELSE
BEGIN
    PRINT '聊天消息表 ChatMessages 已存在';
END

-- 3. 用户聊天室关系表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ChatRoomMembers' AND xtype='U')
BEGIN
    CREATE TABLE ChatRoomMembers (
        MemberID INT IDENTITY(1,1) PRIMARY KEY,
        RoomID INT NOT NULL,
        UserID INT NOT NULL,
        JoinedDate DATETIME NOT NULL DEFAULT GETDATE(),
        LastReadDate DATETIME,
        IsActive BIT NOT NULL DEFAULT 1,
        FOREIGN KEY (RoomID) REFERENCES ChatRooms(RoomID) ON DELETE CASCADE,
        FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE,
        UNIQUE(RoomID, UserID)
    );
    
    CREATE INDEX IX_ChatRoomMembers_RoomID ON ChatRoomMembers(RoomID);
    CREATE INDEX IX_ChatRoomMembers_UserID ON ChatRoomMembers(UserID);
    CREATE INDEX IX_ChatRoomMembers_IsActive ON ChatRoomMembers(IsActive);
    
    PRINT '用户聊天室关系表 ChatRoomMembers 创建成功';
END
ELSE
BEGIN
    PRINT '用户聊天室关系表 ChatRoomMembers 已存在';
END

-- 4. 用户禁言记录表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ChatMuteRecords' AND xtype='U')
BEGIN
    CREATE TABLE ChatMuteRecords (
        MuteID INT IDENTITY(1,1) PRIMARY KEY,
        UserID INT NOT NULL,
        RoomID INT NULL, -- NULL表示全局禁言，否则为特定聊天室禁言
        MutedBy INT NOT NULL, -- 执行禁言的管理员ID
        MuteReason NVARCHAR(500),
        MuteStartDate DATETIME NOT NULL DEFAULT GETDATE(),
        MuteEndDate DATETIME, -- NULL表示永久禁言
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (UserID) REFERENCES Users(UserID),
        FOREIGN KEY (RoomID) REFERENCES ChatRooms(RoomID),
        FOREIGN KEY (MutedBy) REFERENCES Users(UserID)
    );
    
    CREATE INDEX IX_ChatMuteRecords_UserID ON ChatMuteRecords(UserID);
    CREATE INDEX IX_ChatMuteRecords_RoomID ON ChatMuteRecords(RoomID);
    CREATE INDEX IX_ChatMuteRecords_IsActive ON ChatMuteRecords(IsActive);
    CREATE INDEX IX_ChatMuteRecords_MuteEndDate ON ChatMuteRecords(MuteEndDate);
    
    PRINT '用户禁言记录表 ChatMuteRecords 创建成功';
END
ELSE
BEGIN
    PRINT '用户禁言记录表 ChatMuteRecords 已存在';
END

-- 5. 创建默认公共聊天室
IF NOT EXISTS (SELECT * FROM ChatRooms WHERE RoomType = 'Public' AND RoomName = '平台公共聊天室')
BEGIN
    -- 获取第一个管理员用户ID作为创建者
    DECLARE @AdminUserID INT;
    SELECT TOP 1 @AdminUserID = UserID FROM Users WHERE UserType = 'Admin';
    
    IF @AdminUserID IS NOT NULL
    BEGIN
        INSERT INTO ChatRooms (RoomName, RoomType, Description, CreatedBy, CreatedDate, IsActive)
        VALUES ('平台公共聊天室', 'Public', '欢迎所有用户在此交流讨论', @AdminUserID, GETDATE(), 1);
        
        PRINT '默认公共聊天室创建成功';
    END
    ELSE
    BEGIN
        PRINT '未找到管理员用户，跳过创建默认聊天室';
    END
END
ELSE
BEGIN
    PRINT '默认公共聊天室已存在';
END

PRINT '聊天系统数据库表创建完成！';
