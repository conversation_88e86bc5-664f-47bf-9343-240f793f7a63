using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Admin_Dashboard : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 加载系统统计信息
            LoadSystemStatistics();
            
            // 加载系统通知
            LoadSystemNotifications();
        }
    }

    /// <summary>
    /// 加载系统统计信息
    /// </summary>
    private void LoadSystemStatistics()
    {
        try
        {
            // 获取系统统计信息
            DataTable dt = AdminManager.GetSystemStatistics();
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                
                // 设置统计数据
                lblTotalUsers.Text = row["TotalUsers"].ToString();
                lblTotalCarOwners.Text = row["TotalCarOwners"].ToString();
                lblTotalShops.Text = row["TotalRepairShops"].ToString();
                lblTotalAdmins.Text = row["TotalAdmins"].ToString();
                lblTotalCars.Text = row["TotalCars"].ToString();
                lblTotalServices.Text = row["TotalServices"].ToString();
                lblTotalAppointments.Text = row["TotalAppointments"].ToString();
                lblCompletedAppointments.Text = row["CompletedAppointments"].ToString();
                lblTotalReviews.Text = row["TotalReviews"].ToString();
                
                // 格式化平均评分，保留一位小数
                if (row["AverageRating"] != DBNull.Value)
                {
                    decimal avgRating = Convert.ToDecimal(row["AverageRating"]);
                    lblAverageRating.Text = avgRating.ToString("0.0");
                }
                else
                {
                    lblAverageRating.Text = "0.0";
                }
            }

            // 加载聊天系统统计
            LoadChatStatistics();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载系统统计信息时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 加载聊天系统统计
    /// </summary>
    private void LoadChatStatistics()
    {
        try
        {
            // 获取聊天室数量
            string roomQuery = "SELECT COUNT(*) FROM ChatRooms WHERE IsActive = 1";
            object roomResult = DatabaseHelper.ExecuteScalar(roomQuery);
            lblTotalChatRooms.Text = roomResult?.ToString() ?? "0";

            // 获取安全警报数量（这里可以扩展为实际的安全监控）
            lblSecurityAlerts.Text = "0";
        }
        catch (Exception ex)
        {
            lblTotalChatRooms.Text = "0";
            lblSecurityAlerts.Text = "0";
        }
    }

    /// <summary>
    /// 加载系统通知
    /// </summary>
    private void LoadSystemNotifications()
    {
        try
        {
            // 获取系统通知
            DataTable dt = AdminManager.GetSystemNotifications();
            rptNotifications.DataSource = dt;
            rptNotifications.DataBind();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载系统通知时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 添加通知按钮点击事件
    /// </summary>
    protected void lbtnAddNotification_Click(object sender, EventArgs e)
    {
        // 显示添加通知面板
        pnlNotifications.Visible = false;
        pnlEditNotification.Visible = true;
        
        // 清空表单
        txtNotificationTitle.Text = "";
        txtNotificationContent.Text = "";
        chkNotificationActive.Checked = true;
        hfNotificationID.Value = "0";
    }

    /// <summary>
    /// 保存通知按钮点击事件
    /// </summary>
    protected void btnSaveNotification_Click(object sender, EventArgs e)
    {
        try
        {
            int notificationID = Convert.ToInt32(hfNotificationID.Value);
            string title = txtNotificationTitle.Text.Trim();
            string content = txtNotificationContent.Text.Trim();
            bool isActive = chkNotificationActive.Checked;
            
            // 根据ID判断是添加还是更新
            if (notificationID == 0)
            {
                // 添加新通知
                int result = AdminManager.AddSystemNotification(title, content, isActive);
                if (result > 0)
                {
                    lblMessage.Text = "通知添加成功";
                    lblMessage.CssClass = "text-success";
                }
                else
                {
                    lblMessage.Text = "通知添加失败";
                    lblMessage.CssClass = "text-danger";
                }
            }
            else
            {
                // 更新通知
                bool result = AdminManager.UpdateSystemNotification(notificationID, title, content, isActive);
                if (result)
                {
                    lblMessage.Text = "通知更新成功";
                    lblMessage.CssClass = "text-success";
                }
                else
                {
                    lblMessage.Text = "通知更新失败";
                    lblMessage.CssClass = "text-danger";
                }
            }
            
            // 返回通知列表
            pnlNotifications.Visible = true;
            pnlEditNotification.Visible = false;
            
            // 重新加载通知
            LoadSystemNotifications();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "保存通知时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 取消编辑通知按钮点击事件
    /// </summary>
    protected void btnCancelNotification_Click(object sender, EventArgs e)
    {
        // 返回通知列表
        pnlNotifications.Visible = true;
        pnlEditNotification.Visible = false;
    }

    /// <summary>
    /// 通知列表项命令事件
    /// </summary>
    protected void rptNotifications_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        try
        {
            int notificationID = Convert.ToInt32(e.CommandArgument);
            
            switch (e.CommandName)
            {
                case "EditNotification":
                    // 获取通知详情
                    DataTable dt = AdminManager.GetSystemNotifications();
                    DataRow[] rows = dt.Select("NotificationID = " + notificationID);
                    if (rows.Length > 0)
                    {
                        // 显示编辑面板
                        pnlNotifications.Visible = false;
                        pnlEditNotification.Visible = true;
                        
                        // 填充表单
                        txtNotificationTitle.Text = rows[0]["Title"].ToString();
                        txtNotificationContent.Text = rows[0]["Content"].ToString();
                        chkNotificationActive.Checked = Convert.ToBoolean(rows[0]["IsActive"]);
                        hfNotificationID.Value = notificationID.ToString();
                    }
                    break;
                    
                case "DeleteNotification":
                    // 删除通知
                    bool result = AdminManager.DeleteSystemNotification(notificationID);
                    if (result)
                    {
                        lblMessage.Text = "通知删除成功";
                        lblMessage.CssClass = "text-success";
                    }
                    else
                    {
                        lblMessage.Text = "通知删除失败";
                        lblMessage.CssClass = "text-danger";
                    }
                    
                    // 重新加载通知
                    LoadSystemNotifications();
                    break;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "处理通知时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }
} 