using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_Appointments : System.Web.UI.Page
{
    private int userID;
    private int shopID;
    private int? currentAppointmentID = null;
    private string filterStatus = string.Empty;
    private DataTable selectedParts;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }
        
        // 在页面首次加载时，检查并自动取消过期未确认的预约
        if (!IsPostBack)
        {
            try
            {
                int cancelledCount = AppointmentManager.AutoCancelExpiredAppointments();
                if (cancelledCount > 0)
                {
                    // 添加通知消息
                    lblMessage.Text = $"系统已自动取消 {cancelledCount} 个过期未确认的预约。";
                    lblMessage.CssClass = "alert alert-info";
                    lblMessage.Visible = true;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响页面加载
                System.Diagnostics.Debug.WriteLine($"页面加载时自动取消过期预约出错: {ex.Message}");
            }
        }

        // 在回发时恢复currentAppointmentID
        if (IsPostBack)
        {
            if (ViewState["CurrentAppointmentID"] != null)
            {
                int storedID;
                if (int.TryParse(ViewState["CurrentAppointmentID"].ToString(), out storedID))
                {
                    currentAppointmentID = storedID;
                    System.Diagnostics.Debug.WriteLine($"从ViewState恢复预约ID: {currentAppointmentID}");
                }
            }
        }

        // 初始化选中零件数据表
        InitializeSelectedPartsTable();

        if (!IsPostBack)
        {
            // 清除之前的消息
            lblMessage.Visible = false;
            
            // 加载零件下拉列表
            LoadPartsDropDown();
            
            // 检查是否有特定的预约ID
            if (Request.QueryString["id"] != null)
            {
                int appointmentID;
                if (int.TryParse(Request.QueryString["id"], out appointmentID))
                {
                    // 显示特定预约的详情
                    ShowAppointmentDetails(appointmentID);
                    return;
                }
            }

            // 加载预约列表
            LoadAppointments();
        }
    }

    /// <summary>
    /// 初始化选中零件数据表
    /// </summary>
    private void InitializeSelectedPartsTable()
    {
        selectedParts = new DataTable();
        selectedParts.Columns.Add("PartID", typeof(int));
        selectedParts.Columns.Add("PartName", typeof(string));
        selectedParts.Columns.Add("Quantity", typeof(int));
        selectedParts.Columns.Add("UnitPrice", typeof(decimal));
        selectedParts.Columns.Add("TotalPrice", typeof(decimal));
        
        // 从ViewState恢复数据（如果存在）
        if (ViewState["SelectedParts"] != null)
        {
            selectedParts = (DataTable)ViewState["SelectedParts"];
        }
        
        // 绑定到GridView
        gvSelectedParts.DataSource = selectedParts;
        gvSelectedParts.DataBind();
        
        // 更新总费用
        UpdatePartsCostTotal();
    }

    /// <summary>
    /// 加载零件下拉列表
    /// </summary>
    private void LoadPartsDropDown()
    {
        try
        {
            DataTable parts = PartsManager.GetPartsByShopID(shopID);
            ddlParts.Items.Clear();
            ddlParts.Items.Add(new ListItem("请选择零件", ""));
            
            foreach (DataRow row in parts.Rows)
            {
                string partName = row["PartName"].ToString();
                string price = Convert.ToDecimal(row["Price"]).ToString("N2");
                string stock = row["StockQuantity"].ToString();
                string text = $"{partName} (¥{price}) - 库存:{stock}";
                ddlParts.Items.Add(new ListItem(text, row["PartID"].ToString()));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载零件下拉列表时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新零件费用总计
    /// </summary>
    private void UpdatePartsCostTotal()
    {
        decimal total = 0;
        foreach (DataRow row in selectedParts.Rows)
        {
            total += Convert.ToDecimal(row["TotalPrice"]);
        }
        
        lblPartsCostTotal.Text = total.ToString("N2");
        hfPartsCostTotal.Value = total.ToString("F2");
        
        // 保存到ViewState
        ViewState["SelectedParts"] = selectedParts;
    }

    /// <summary>
    /// 加载预约列表
    /// </summary>
    private void LoadAppointments()
    {
        // 从会话中获取过滤状态（如果有）
        if (Session["AppointmentFilterStatus"] != null)
        {
            filterStatus = Session["AppointmentFilterStatus"].ToString();
            ddlFilterStatus.SelectedValue = filterStatus;
        }

        // 根据过滤条件获取预约列表
        DataTable appointments = GetFilteredAppointments();
        
        gvAppointments.DataSource = appointments;
        gvAppointments.DataBind();
    }

    /// <summary>
    /// 根据过滤条件获取预约列表
    /// </summary>
    private DataTable GetFilteredAppointments()
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID";

        if (!string.IsNullOrEmpty(filterStatus))
        {
            query += " AND a.Status = @Status";
        }

        query += " ORDER BY a.AppointmentDate DESC";

        System.Data.SqlClient.SqlParameter[] parameters;
        if (!string.IsNullOrEmpty(filterStatus))
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID),
                new System.Data.SqlClient.SqlParameter("@Status", filterStatus)
            };
        }
        else
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };
        }

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 显示预约详情
    /// </summary>
    private void ShowAppointmentDetails(int appointmentID)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"显示预约详情 - 预约ID: {appointmentID}");
            
            string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                            c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                            co.FullName AS OwnerName, s.ServiceName, s.EstimatedTime, s.BasePrice
                            FROM Appointments a
                            INNER JOIN Cars c ON a.CarID = c.CarID
                            INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                            INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                            WHERE a.AppointmentID = @AppointmentID AND a.ShopID = @ShopID";

            System.Data.SqlClient.SqlParameter[] parameters = 
            {
                new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };

            DataTable appointmentDetails = DatabaseHelper.ExecuteQuery(query, parameters);
            
            if (appointmentDetails != null && appointmentDetails.Rows.Count > 0)
            {
                DataRow appointment = appointmentDetails.Rows[0];
                
                // 保存当前预约ID到成员变量和ViewState
                currentAppointmentID = appointmentID;
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                // 保存预计时间到ViewState，用于计算工时费
                int estimatedTime = Convert.ToInt32(appointment["EstimatedTime"]);
                ViewState["EstimatedTime"] = estimatedTime;
                
                System.Diagnostics.Debug.WriteLine($"成功保存预约ID到ViewState: {appointmentID}");
                System.Diagnostics.Debug.WriteLine($"保存预计时间: {estimatedTime} 分钟");
                
                // 填充详情页面
                lblAppointmentID.Text = appointment["AppointmentID"].ToString();
                lblAppointmentDate.Text = Convert.ToDateTime(appointment["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                lblOwnerName.Text = appointment["OwnerName"].ToString();
                lblCarInfo.Text = appointment["CarInfo"].ToString();
                lblServiceName.Text = appointment["ServiceName"].ToString();
                lblEstimatedTime.Text = appointment["EstimatedTime"].ToString();
                lblBasePrice.Text = string.Format("¥{0:N2}", Convert.ToDecimal(appointment["BasePrice"]));
                
                string status = appointment["Status"].ToString();
                lblStatus.Text = GetStatusText(status);
                lblStatus.CssClass = GetStatusClass(status);
                
                lblDescription.Text = appointment["Description"] != DBNull.Value ? 
                    appointment["Description"].ToString() : "无";
                
                // 根据状态显示不同的按钮
                btnConfirm.Visible = status == "Pending";
                btnComplete.Visible = status == "Confirmed";
                
                // 显示详情面板，隐藏列表和其他面板
                pnlAppointmentDetails.Visible = true;
                gvAppointments.Visible = false;
                pnlCompleteForm.Visible = false;
            }
            else
            {
                // 无法找到预约或不属于该维修店
                System.Diagnostics.Debug.WriteLine($"未找到预约或预约不属于此维修店 - 预约ID: {appointmentID}");
                
                // 清除当前预约ID
                currentAppointmentID = null;
                ViewState["CurrentAppointmentID"] = null;
                ViewState["EstimatedTime"] = null;
                
                lblMessage.Text = "找不到此预约信息或该预约不属于您的维修店";
                lblMessage.CssClass = "alert alert-warning";
                lblMessage.Visible = true;
                
                // 返回列表页面
                pnlAppointmentDetails.Visible = false;
                gvAppointments.Visible = true;
                LoadAppointments();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"显示预约详情时出错: {ex.Message}");
            
            lblMessage.Text = "加载预约详情时出错";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            
            // 返回列表页面
            pnlAppointmentDetails.Visible = false;
            gvAppointments.Visible = true;
            LoadAppointments();
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBack_Click(object sender, EventArgs e)
    {
        // 隐藏详情面板，显示列表
        pnlAppointmentDetails.Visible = false;
        pnlCompleteForm.Visible = false;
        gvAppointments.Visible = true;
        
        // 清除ViewState
        ViewState["CurrentAppointmentID"] = null;
        ViewState["EstimatedTime"] = null;
        currentAppointmentID = null;
        
        // 重新加载预约列表
        LoadAppointments();
    }

    /// <summary>
    /// 确认预约按钮点击事件
    /// </summary>
    protected void btnConfirm_Click(object sender, EventArgs e)
    {
        if (currentAppointmentID.HasValue)
        {
            bool success = ShopManager.ConfirmAppointment(currentAppointmentID.Value);
            if (success)
            {
                // 成功确认，刷新详情页
                ShowAppointmentDetails(currentAppointmentID.Value);
                lblMessage.Text = "预约已成功确认。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "确认失败，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
    }

    /// <summary>
    /// 添加零件按钮点击事件
    /// </summary>
    protected void btnAddPart_Click(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(ddlParts.SelectedValue))
            {
                lblMessage.Text = "请选择要添加的零件";
                lblMessage.CssClass = "alert alert-warning";
                lblMessage.Visible = true;
                return;
            }

            int partID = Convert.ToInt32(ddlParts.SelectedValue);
            int quantity = 1;
            
            if (!string.IsNullOrEmpty(txtQuantity.Text))
            {
                if (!int.TryParse(txtQuantity.Text, out quantity) || quantity <= 0)
                {
                    lblMessage.Text = "请输入有效的数量";
                    lblMessage.CssClass = "alert alert-warning";
                    lblMessage.Visible = true;
                    return;
                }
            }

            // 获取零件信息
            DataTable partInfo = PartsManager.GetPartByID(partID);
            if (partInfo == null || partInfo.Rows.Count == 0)
            {
                lblMessage.Text = "找不到选择的零件";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            DataRow part = partInfo.Rows[0];
            string partName = part["PartName"].ToString();
            decimal unitPrice = Convert.ToDecimal(part["Price"]);
            int stockQuantity = Convert.ToInt32(part["StockQuantity"]);

            // 检查库存
            if (quantity > stockQuantity)
            {
                lblMessage.Text = $"库存不足，当前库存：{stockQuantity}";
                lblMessage.CssClass = "alert alert-warning";
                lblMessage.Visible = true;
                return;
            }

            // 检查是否已经添加过该零件
            DataRow[] existingRows = selectedParts.Select($"PartID = {partID}");
            if (existingRows.Length > 0)
            {
                // 更新数量
                DataRow existingRow = existingRows[0];
                int newQuantity = Convert.ToInt32(existingRow["Quantity"]) + quantity;
                
                if (newQuantity > stockQuantity)
                {
                    lblMessage.Text = $"总数量超过库存，当前库存：{stockQuantity}";
                    lblMessage.CssClass = "alert alert-warning";
                    lblMessage.Visible = true;
                    return;
                }
                
                existingRow["Quantity"] = newQuantity;
                existingRow["TotalPrice"] = newQuantity * unitPrice;
            }
            else
            {
                // 添加新行
                DataRow newRow = selectedParts.NewRow();
                newRow["PartID"] = partID;
                newRow["PartName"] = partName;
                newRow["Quantity"] = quantity;
                newRow["UnitPrice"] = unitPrice;
                newRow["TotalPrice"] = quantity * unitPrice;
                selectedParts.Rows.Add(newRow);
            }

            // 重新绑定GridView并更新总费用
            gvSelectedParts.DataSource = selectedParts;
            gvSelectedParts.DataBind();
            UpdatePartsCostTotal();

            // 重置选择
            ddlParts.SelectedIndex = 0;
            txtQuantity.Text = "1";
            lblMessage.Visible = false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加零件时出错: {ex.Message}");
            lblMessage.Text = "添加零件时出错";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 选中零件GridView行命令事件
    /// </summary>
    protected void gvSelectedParts_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "RemovePart")
        {
            try
            {
                int partID = Convert.ToInt32(e.CommandArgument);
                
                // 从数据表中移除该零件
                DataRow[] rowsToDelete = selectedParts.Select($"PartID = {partID}");
                if (rowsToDelete.Length > 0)
                {
                    selectedParts.Rows.Remove(rowsToDelete[0]);
                    
                    // 重新绑定GridView并更新总费用
                    gvSelectedParts.DataSource = selectedParts;
                    gvSelectedParts.DataBind();
                    UpdatePartsCostTotal();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除零件时出错: {ex.Message}");
                lblMessage.Text = "移除零件时出错";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
    }

    /// <summary>
    /// 完成维修按钮点击事件
    /// </summary>
    protected void btnComplete_Click(object sender, EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"完成维修按钮被点击，当前预约ID: {(currentAppointmentID.HasValue ? currentAppointmentID.Value.ToString() : "null")}");
            
            if (currentAppointmentID.HasValue)
            {
                // 确保当前预约ID有效
                int appointmentID = currentAppointmentID.Value;
                
                // 将ID存入ViewState防止丢失
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                System.Diagnostics.Debug.WriteLine($"显示完成维修表单 - 预约ID: {appointmentID}");
                
                // 显示完成维修表单
                pnlAppointmentDetails.Visible = false;
                pnlCompleteForm.Visible = true;
                gvAppointments.Visible = false;
                
                // 初始化表单
                txtDiagnosisDetails.Text = string.Empty;
                LoadTechnicians();
                ddlTechnician.SelectedIndex = 0;
                
                // 根据预计时间自动计算工时费
                SetLaborCostFromEstimatedTime();
                
                // 清空选中的零件
                selectedParts.Clear();
                gvSelectedParts.DataSource = selectedParts;
                gvSelectedParts.DataBind();
                UpdatePartsCostTotal();
                
                // 重新加载零件下拉列表
                LoadPartsDropDown();
                txtQuantity.Text = "1";

                // 确保消息标签隐藏
                lblMessage.Visible = false;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("无法显示完成维修表单 - 预约ID为空");
                lblMessage.Text = "无法处理此预约，预约ID无效";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                
                // 返回列表页面
                pnlAppointmentDetails.Visible = false;
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                LoadAppointments();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"完成维修按钮点击事件出错: {ex.Message}");
            lblMessage.Text = "处理请求时出错，请稍后再试";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            
            // 返回列表页面
            pnlAppointmentDetails.Visible = false;
            pnlCompleteForm.Visible = false;
            gvAppointments.Visible = true;
            LoadAppointments();
        }
    }

    /// <summary>
    /// 提交完成记录按钮点击事件
    /// </summary>
    protected void btnSubmitComplete_Click(object sender, EventArgs e)
    {
        try
        {
            // 验证预约ID
            if (!currentAppointmentID.HasValue)
            {
                lblMessage.Text = "预约ID无效";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            // 记录操作开始
            System.Diagnostics.Debug.WriteLine($"开始提交完成维修记录 - 预约ID: {currentAppointmentID.Value}");

            // 获取表单数据
            string diagnosisDetails = txtDiagnosisDetails.Text.Trim();
            decimal laborCost = 0;
            decimal partsCost = 0;

            // 尝试转换费用
            try
            {
                laborCost = Convert.ToDecimal(txtLaborCost.Text);
                partsCost = Convert.ToDecimal(hfPartsCostTotal.Value);
            }
            catch (FormatException)
            {
                lblMessage.Text = "费用金额格式无效";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            string technicianName = "";
            if (ddlTechnician.SelectedValue != "")
            {
                // 获取选中技师的姓名
                DataTable technicianTable = ShopManager.GetEmployeeDetails(Convert.ToInt32(ddlTechnician.SelectedValue));
                if (technicianTable.Rows.Count > 0)
                {
                    technicianName = technicianTable.Rows[0]["EmployeeName"].ToString();
                }
            }

            if (string.IsNullOrEmpty(diagnosisDetails))
            {
                lblMessage.Text = "诊断详情不能为空";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            if (string.IsNullOrEmpty(technicianName))
            {
                lblMessage.Text = "请选择技师";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            // 验证工时费是否正确计算
            if (laborCost <= 0)
            {
                lblMessage.Text = "工时费计算错误，请重新加载页面";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            System.Diagnostics.Debug.WriteLine($"表单数据验证通过，准备调用CompleteAppointment方法");
            System.Diagnostics.Debug.WriteLine($"工时费: ¥{laborCost:F2}, 零件费: ¥{partsCost:F2}");
            
            // 生成零件描述文本
            string partsReplaced = GeneratePartsDescription();
            
            // 调用完成预约方法
            int recordID = ShopManager.CompleteAppointment(
                currentAppointmentID.Value,
                diagnosisDetails,
                partsReplaced,
                laborCost,
                partsCost,
                technicianName
            );
            
            // 如果成功创建了维修记录，保存零件使用记录
            if (recordID > 0)
            {
                SaveServiceRecordParts(recordID);
                UpdatePartsStock();
            }
            
            System.Diagnostics.Debug.WriteLine($"CompleteAppointment方法返回值: {recordID}");
            
            if (recordID > 0)
            {
                // 完成成功，清除预约ID和输入表单
                currentAppointmentID = null;
                ViewState["CurrentAppointmentID"] = null;
                ViewState["EstimatedTime"] = null;
                
                // 返回列表页
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                
                // 重新加载预约列表
                LoadAppointments();
                
                // 显示成功消息
                lblMessage.Text = "维修已完成，记录已保存！工时费已根据预计时间自动计算。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "操作失败，维修记录保存失败。请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
        catch (Exception ex)
        {
            // 记录详细错误
            System.Diagnostics.Debug.WriteLine($"完成维修表单提交时出错: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            
            lblMessage.Text = $"提交过程中发生错误: {ex.Message}";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 取消完成表单按钮点击事件
    /// </summary>
    protected void btnCancelComplete_Click(object sender, EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("取消完成维修记录操作");
            
            // 返回预约详情页
            if (currentAppointmentID.HasValue)
            {
                System.Diagnostics.Debug.WriteLine($"返回到预约详情页 - 预约ID: {currentAppointmentID.Value}");
                pnlCompleteForm.Visible = false;
                
                // 清除可能的错误消息
                lblMessage.Visible = false;
                
                // 重新显示详情页
                ShowAppointmentDetails(currentAppointmentID.Value);
            }
            else
            {
                // 如果ID无效，返回列表页
                System.Diagnostics.Debug.WriteLine("预约ID无效，返回列表页");
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                
                // 清除ViewState
                ViewState["CurrentAppointmentID"] = null;
                ViewState["EstimatedTime"] = null;
                
                LoadAppointments();
                
                lblMessage.Text = "操作已取消";
                lblMessage.CssClass = "alert alert-info";
                lblMessage.Visible = true;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"取消完成表单时出错: {ex.Message}");
            
            // 确保返回列表页面
            pnlCompleteForm.Visible = false;
            gvAppointments.Visible = true;
            
            // 清除ViewState
            ViewState["CurrentAppointmentID"] = null;
            ViewState["EstimatedTime"] = null;
            
            LoadAppointments();
            
            lblMessage.Text = "处理请求时发生错误，已返回列表页";
            lblMessage.CssClass = "alert alert-warning";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvAppointments_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int appointmentID = Convert.ToInt32(e.CommandArgument);
        
        if (e.CommandName == "ViewAppointment")
        {
            // 查看预约详情
            ShowAppointmentDetails(appointmentID);
        }
        else if (e.CommandName == "ConfirmAppointment")
        {
            // 确认预约
            bool success = ShopManager.ConfirmAppointment(appointmentID);
            if (success)
            {
                // 成功确认，刷新列表
                LoadAppointments();
                lblMessage.Text = "预约已成功确认。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "确认失败，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
        else if (e.CommandName == "CompleteAppointment")
        {
            try
            {
                // 保存当前预约ID，并显示完成维修表单
                currentAppointmentID = appointmentID;
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                System.Diagnostics.Debug.WriteLine($"列表中点击完成按钮 - 预约ID: {appointmentID}");
                
                // 先加载预约详情以验证预约状态
                string query = @"SELECT Status FROM Appointments 
                                WHERE AppointmentID = @AppointmentID AND ShopID = @ShopID";
                
                System.Data.SqlClient.SqlParameter[] parameters = 
                {
                    new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                    new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
                };
                
                DataTable result = DatabaseHelper.ExecuteQuery(query, parameters);
                
                if (result != null && result.Rows.Count > 0)
                {
                    string status = result.Rows[0]["Status"].ToString();
                    
                    if (status == "Confirmed")
                    {
                        // 显示完成维修表单，不需要先显示详情
                        pnlAppointmentDetails.Visible = false;
                        pnlCompleteForm.Visible = true;
                        gvAppointments.Visible = false;
                        
                        // 初始化表单
                        txtDiagnosisDetails.Text = string.Empty;
                        LoadTechnicians();
                        ddlTechnician.SelectedIndex = 0;
                        
                        // 根据预计时间自动计算工时费
                        SetLaborCostFromEstimatedTime();
                        
                        // 清空选中的零件
                        selectedParts.Clear();
                        gvSelectedParts.DataSource = selectedParts;
                        gvSelectedParts.DataBind();
                        UpdatePartsCostTotal();
                        
                        // 重新加载零件下拉列表
                        LoadPartsDropDown();
                        txtQuantity.Text = "1";
                        
                        // 确保消息标签隐藏
                        lblMessage.Visible = false;
                    }
                    else
                    {
                        lblMessage.Text = "只能对已确认的预约进行完成操作";
                        lblMessage.CssClass = "alert alert-warning";
                        lblMessage.Visible = true;
                    }
                }
                else
                {
                    lblMessage.Text = "找不到对应的预约信息";
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理CompleteAppointment命令出错: {ex.Message}");
                lblMessage.Text = "无法显示维修表单，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
        else if (e.CommandName == "DeleteAppointment")
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"删除预约 - 预约ID: {appointmentID}");
                
                // 删除预约
                bool success = DeleteAppointment(appointmentID);
                
                if (success)
                {
                    // 删除成功，刷新列表
                    LoadAppointments();
                    lblMessage.Text = "预约已成功删除。";
                    lblMessage.CssClass = "alert alert-success";
                    lblMessage.Visible = true;
                }
                else
                {
                    lblMessage.Text = "删除失败，请稍后再试。";
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除预约时出错: {ex.Message}");
                lblMessage.Text = "删除预约时发生错误，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
    }

    /// <summary>
    /// 过滤器选择变更事件
    /// </summary>
    protected void ddlFilterStatus_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 保存过滤状态到会话
        filterStatus = ddlFilterStatus.SelectedValue;
        Session["AppointmentFilterStatus"] = filterStatus;
        
        // 重新加载预约列表
        LoadAppointments();
    }

    /// <summary>
    /// GridView分页事件
    /// </summary>
    protected void gvAppointments_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvAppointments.PageIndex = e.NewPageIndex;
        LoadAppointments();
    }

    /// <summary>
    /// 获取状态文本
    /// </summary>
    public string GetStatusText(string status)
    {
        switch (status)
        {
            case "Pending":
                return "待处理";
            case "Confirmed":
                return "已确认";
            case "Completed":
                return "已完成";
            case "Cancelled":
                return "已取消";
            default:
                return status;
        }
    }

    /// <summary>
    /// <summary>
    /// 获取状态CSS类
    /// </summary>
    public string GetStatusClass(string status)
    {
        switch (status)
        {
            case "Pending":
                return "badge badge-warning";
            case "Confirmed":
                return "badge badge-success";
            case "Completed":
                return "badge badge-primary";
            case "Cancelled":
                return "badge badge-danger";
            default:
                return "badge badge-secondary";
        }
    }

    /// <summary>
    /// 删除预约
    /// </summary>
    private bool DeleteAppointment(int appointmentID)
    {
        try
        {
            // 首先检查预约是否属于当前维修店
            string checkQuery = @"SELECT Status FROM Appointments 
                                 WHERE AppointmentID = @AppointmentID AND ShopID = @ShopID";
            
            System.Data.SqlClient.SqlParameter[] checkParams = 
            {
                new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };
            
            DataTable result = DatabaseHelper.ExecuteQuery(checkQuery, checkParams);
            
            if (result == null || result.Rows.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine($"预约不存在或不属于当前维修店 - 预约ID: {appointmentID}");
                return false;
            }
            
            string status = result.Rows[0]["Status"].ToString();
            
            // 检查预约状态，已完成的预约不能删除
            if (status == "Completed")
            {
                System.Diagnostics.Debug.WriteLine($"不能删除已完成的预约 - 预约ID: {appointmentID}");
                lblMessage.Text = "不能删除已完成的预约。";
                lblMessage.CssClass = "alert alert-warning";
                lblMessage.Visible = true;
                return false;
            }
            
            // 开始事务删除预约
            // 如果预约已完成，需要先删除相关的维修记录
            if (status == "Completed")
            {
                string deleteRecordQuery = @"DELETE FROM ServiceRecords 
                                            WHERE AppointmentID = @AppointmentID";
                
                System.Data.SqlClient.SqlParameter[] deleteRecordParams = 
                {
                    new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID)
                };
                
                int recordResult = DatabaseHelper.ExecuteNonQuery(deleteRecordQuery, deleteRecordParams);
                System.Diagnostics.Debug.WriteLine($"删除维修记录结果: {recordResult}");
            }
            
            // 删除预约记录
            string deleteAppointmentQuery = @"DELETE FROM Appointments 
                                             WHERE AppointmentID = @AppointmentID AND ShopID = @ShopID";
            
            System.Data.SqlClient.SqlParameter[] deleteAppointmentParams = 
            {
                new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };
            
            int rowsAffected = DatabaseHelper.ExecuteNonQuery(deleteAppointmentQuery, deleteAppointmentParams);
            
            if (rowsAffected > 0)
            {
                System.Diagnostics.Debug.WriteLine($"预约删除成功 - 预约ID: {appointmentID}");
                return true;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"预约删除失败，没有找到匹配的记录 - 预约ID: {appointmentID}");
                return false;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除预约方法出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 生成零件描述文本
    /// </summary>
    /// <returns>零件描述</returns>
    private string GeneratePartsDescription()
    {
        if (selectedParts == null || selectedParts.Rows.Count == 0)
            return "无";

        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        foreach (DataRow row in selectedParts.Rows)
        {
            string partName = row["PartName"].ToString();
            int quantity = Convert.ToInt32(row["Quantity"]);
            decimal unitPrice = Convert.ToDecimal(row["UnitPrice"]);
            
            sb.AppendLine($"{partName} x{quantity} (¥{unitPrice:N2})");
        }
        
        return sb.ToString().Trim();
    }

    /// <summary>
    /// 保存维修记录零件使用信息
    /// </summary>
    /// <param name="recordID">维修记录ID</param>
    private void SaveServiceRecordParts(int recordID)
    {
        try
        {
            foreach (DataRow row in selectedParts.Rows)
            {
                int partID = Convert.ToInt32(row["PartID"]);
                int quantity = Convert.ToInt32(row["Quantity"]);
                decimal unitPrice = Convert.ToDecimal(row["UnitPrice"]);

                string query = @"INSERT INTO ServiceRecordParts (RecordID, PartID, Quantity, UnitPrice)
                                VALUES (@RecordID, @PartID, @Quantity, @UnitPrice)";

                System.Data.SqlClient.SqlParameter[] parameters = 
                {
                    new System.Data.SqlClient.SqlParameter("@RecordID", recordID),
                    new System.Data.SqlClient.SqlParameter("@PartID", partID),
                    new System.Data.SqlClient.SqlParameter("@Quantity", quantity),
                    new System.Data.SqlClient.SqlParameter("@UnitPrice", unitPrice)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存零件使用记录时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新零件库存
    /// </summary>
    private void UpdatePartsStock()
    {
        try
        {
            foreach (DataRow row in selectedParts.Rows)
            {
                int partID = Convert.ToInt32(row["PartID"]);
                int quantity = Convert.ToInt32(row["Quantity"]);
                
                PartsManager.UpdatePartStock(partID, quantity);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新零件库存时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 根据预计时间设置工时费（每分钟1元）
    /// </summary>
    private void SetLaborCostFromEstimatedTime()
    {
        try
        {
            // 从ViewState获取预计时间
            if (ViewState["EstimatedTime"] != null)
            {
                int estimatedTime = Convert.ToInt32(ViewState["EstimatedTime"]);
                decimal laborCost = estimatedTime * 1.0m; // 每分钟1元
                txtLaborCost.Text = laborCost.ToString("F2");
                
                System.Diagnostics.Debug.WriteLine($"根据预计时间设置工时费: {estimatedTime} 分钟 = ¥{laborCost:F2}");
            }
            else
            {
                // 如果没有预计时间信息，需要重新获取
                if (currentAppointmentID.HasValue)
                {
                    string query = @"SELECT s.EstimatedTime 
                                    FROM Appointments a
                                    INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                                    WHERE a.AppointmentID = @AppointmentID AND a.ShopID = @ShopID";

                    System.Data.SqlClient.SqlParameter[] parameters = 
                    {
                        new System.Data.SqlClient.SqlParameter("@AppointmentID", currentAppointmentID.Value),
                        new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
                    };

                    DataTable result = DatabaseHelper.ExecuteQuery(query, parameters);
                    
                    if (result != null && result.Rows.Count > 0)
                    {
                        int estimatedTime = Convert.ToInt32(result.Rows[0]["EstimatedTime"]);
                        ViewState["EstimatedTime"] = estimatedTime;
                        decimal laborCost = estimatedTime * 1.0m; // 每分钟1元
                        txtLaborCost.Text = laborCost.ToString("F2");
                        
                        System.Diagnostics.Debug.WriteLine($"重新获取预计时间并设置工时费: {estimatedTime} 分钟 = ¥{laborCost:F2}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("无法获取预计时间信息，使用默认工时费");
                        txtLaborCost.Text = "0.00";
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("预约ID无效，无法设置工时费");
                    txtLaborCost.Text = "0.00";
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置工时费时出错: {ex.Message}");
            txtLaborCost.Text = "0.00";
        }
    }

    /// <summary>
    /// 加载技师列表
    /// </summary>
    private void LoadTechnicians()
    {
        try
        {
            ddlTechnician.Items.Clear();
            ddlTechnician.Items.Add(new ListItem("-- 请选择技师 --", ""));

            DataTable techniciansTable = ShopManager.GetActiveEmployees(shopID);
            foreach (DataRow row in techniciansTable.Rows)
            {
                string employeeID = row["EmployeeID"].ToString();
                string employeeName = row["EmployeeName"].ToString();
                string position = row["Position"].ToString();
                string displayText = $"{employeeName} ({position})";

                ddlTechnician.Items.Add(new ListItem(displayText, employeeID));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载技师列表时出错: {ex.Message}");
            // 如果加载失败，至少保留默认选项
            if (ddlTechnician.Items.Count == 0)
            {
                ddlTechnician.Items.Add(new ListItem("-- 请选择技师 --", ""));
            }
        }
    }
}
