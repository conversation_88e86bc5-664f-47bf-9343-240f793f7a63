-- 创建员工表
CREATE TABLE Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    ShopID INT NOT NULL,
    EmployeeName NVARCHAR(50) NOT NULL,
    Position NVARCHAR(50) NOT NULL DEFAULT '技师',
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    HireDate DATE NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY (ShopID) REFERENCES RepairShops(ShopID) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IX_Employees_ShopID ON Employees(ShopID);
CREATE INDEX IX_Employees_IsActive ON Employees(IsActive);

-- 插入示例数据（可选）
-- INSERT INTO Employees (ShopID, EmployeeName, Position, Phone) 
-- VALUES (1, '张师傅', '高级技师', '13800138001');
-- INSERT INTO Employees (ShopID, EmployeeName, Position, Phone) 
-- VALUES (1, '李师傅', '技师', '13800138002');