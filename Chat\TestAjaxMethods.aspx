<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TestAjaxMethods.aspx.cs" Inherits="Chat_TestAjaxMethods" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Ajax方法测试</title>
    <meta charset="utf-8">
    <script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        button { padding: 8px 16px; margin: 5px; }
        input[type="text"] { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>聊天系统Ajax方法测试</h1>
        
        <div class="test-section">
            <h3>1. 测试CheckNewMessages方法</h3>
            <p>房间ID: <input type="text" id="roomId1" value="1" /></p>
            <p>最后消息时间: <input type="text" id="lastTime1" value="2024-01-01 00:00:00" /></p>
            <button type="button" onclick="testCheckNewMessages()">测试检查新消息</button>
            <div class="result" id="result1"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试GetNewMessages方法</h3>
            <p>房间ID: <input type="text" id="roomId2" value="1" /></p>
            <p>最后消息时间: <input type="text" id="lastTime2" value="2024-01-01 00:00:00" /></p>
            <button type="button" onclick="testGetNewMessages()">测试获取新消息</button>
            <div class="result" id="result2"></div>
        </div>

        <div class="test-section">
            <h3>3. 模拟聊天刷新流程</h3>
            <p>房间ID: <input type="text" id="roomId3" value="1" /></p>
            <button type="button" onclick="startSimulation()">开始模拟</button>
            <button type="button" onclick="stopSimulation()">停止模拟</button>
            <div class="result" id="result3"></div>
        </div>
    </form>

    <script type="text/javascript">
        var simulationInterval = null;

        function testCheckNewMessages() {
            var roomId = document.getElementById('roomId1').value;
            var lastTime = document.getElementById('lastTime1').value;
            
            $.ajax({
                type: "POST",
                url: "ChatMain.aspx/CheckNewMessages",
                data: JSON.stringify({ 
                    roomID: parseInt(roomId), 
                    lastMessageTime: lastTime 
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    document.getElementById('result1').innerHTML = 
                        '<strong>成功:</strong><br/>' + 
                        '<pre>' + JSON.stringify(JSON.parse(response.d), null, 2) + '</pre>';
                },
                error: function(xhr, status, error) {
                    document.getElementById('result1').innerHTML = 
                        '<strong>错误:</strong><br/>' + 
                        'Status: ' + status + '<br/>' +
                        'Error: ' + error + '<br/>' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        function testGetNewMessages() {
            var roomId = document.getElementById('roomId2').value;
            var lastTime = document.getElementById('lastTime2').value;
            
            $.ajax({
                type: "POST",
                url: "ChatMain.aspx/GetNewMessages",
                data: JSON.stringify({ 
                    roomID: parseInt(roomId), 
                    lastMessageTime: lastTime 
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    document.getElementById('result2').innerHTML = 
                        '<strong>成功:</strong><br/>' + 
                        '<pre>' + JSON.stringify(JSON.parse(response.d), null, 2) + '</pre>';
                },
                error: function(xhr, status, error) {
                    document.getElementById('result2').innerHTML = 
                        '<strong>错误:</strong><br/>' + 
                        'Status: ' + status + '<br/>' +
                        'Error: ' + error + '<br/>' +
                        'Response: ' + xhr.responseText;
                }
            });
        }

        function startSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
            }

            var roomId = document.getElementById('roomId3').value;
            var lastTime = new Date().toISOString();
            var resultDiv = document.getElementById('result3');
            
            resultDiv.innerHTML = '<strong>开始模拟聊天刷新...</strong><br/>';
            
            simulationInterval = setInterval(function() {
                // 先检查是否有新消息
                $.ajax({
                    type: "POST",
                    url: "ChatMain.aspx/CheckNewMessages",
                    data: JSON.stringify({ 
                        roomID: parseInt(roomId), 
                        lastMessageTime: lastTime 
                    }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function(response) {
                        var result = JSON.parse(response.d);
                        var timestamp = new Date().toLocaleTimeString();
                        
                        if (result.success && result.hasNewMessages) {
                            resultDiv.innerHTML += timestamp + ' - 发现新消息，正在加载...<br/>';
                            
                            // 加载新消息
                            $.ajax({
                                type: "POST",
                                url: "ChatMain.aspx/GetNewMessages",
                                data: JSON.stringify({ 
                                    roomID: parseInt(roomId), 
                                    lastMessageTime: lastTime 
                                }),
                                contentType: "application/json; charset=utf-8",
                                dataType: "json",
                                success: function(response2) {
                                    var result2 = JSON.parse(response2.d);
                                    if (result2.success && result2.messages) {
                                        resultDiv.innerHTML += timestamp + ' - 加载了 ' + result2.messages.length + ' 条新消息<br/>';
                                        if (result2.messages.length > 0) {
                                            lastTime = result2.messages[result2.messages.length - 1].SentDate;
                                        }
                                    }
                                }
                            });
                        } else {
                            resultDiv.innerHTML += timestamp + ' - 无新消息<br/>';
                        }
                        
                        // 保持最新的10行日志
                        var lines = resultDiv.innerHTML.split('<br/>');
                        if (lines.length > 10) {
                            resultDiv.innerHTML = lines.slice(-10).join('<br/>');
                        }
                    },
                    error: function(xhr, status, error) {
                        var timestamp = new Date().toLocaleTimeString();
                        resultDiv.innerHTML += timestamp + ' - 错误: ' + error + '<br/>';
                    }
                });
            }, 3000); // 每3秒检查一次
        }

        function stopSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
                simulationInterval = null;
                document.getElementById('result3').innerHTML += '<strong>模拟已停止</strong><br/>';
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
            }
        });
    </script>
</body>
</html>
