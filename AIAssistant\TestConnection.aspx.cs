using System;
using System.Web.UI;
using System.Threading.Tasks;
using System.Text;

public partial class AIAssistant_TestConnection : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            AddResult("页面加载完成，准备进行测试...", "loading");
        }
    }

    protected async void btnTestAI_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试AI服务连接...", "loading");
            
            var response = await AIService.GetAIResponseAsync("测试", "请简单回复'连接正常'");
            
            if (!string.IsNullOrEmpty(response) && !response.Contains("抱歉"))
            {
                AddResult($"✅ AI服务连接成功！响应: {response}", "success");
            }
            else
            {
                AddResult($"❌ AI服务响应异常: {response}", "error");
            }
        }
        catch (Exception ex)
        {
            AddResult($"❌ AI服务连接失败: {ex.Message}", "error");
        }
    }

    protected async void btnTestAmap_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试高德地图API连接...", "loading");
            
            var location = await AmapService.GeocodeAsync("北京市天安门");
            
            if (location != null)
            {
                AddResult($"✅ 高德地图API连接成功！地址: {location.Address}, 坐标: {location.Longitude}, {location.Latitude}", "success");
            }
            else
            {
                AddResult("❌ 高德地图API响应为空", "error");
            }
        }
        catch (Exception ex)
        {
            AddResult($"❌ 高德地图API连接失败: {ex.Message}", "error");
        }
    }

    protected void btnTestDB_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试数据库连接...", "loading");
            
            var shops = ShopManager.GetAllShopsForFrontend();
            var shopsWithLocation = 0;
            
            foreach (System.Data.DataRow shop in shops.Rows)
            {
                if (shop["Longitude"] != DBNull.Value && shop["Latitude"] != DBNull.Value)
                {
                    shopsWithLocation++;
                }
            }
            
            AddResult($"✅ 数据库连接成功！共有 {shops.Rows.Count} 个维修店，其中 {shopsWithLocation} 个有地理位置信息", "success");
        }
        catch (Exception ex)
        {
            AddResult($"❌ 数据库连接失败: {ex.Message}", "error");
        }
    }

    protected async void btnTestGeocode_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试地理编码功能...", "loading");
            
            var location = await AmapService.GeocodeAsync("北京市朝阳区");
            
            if (location != null)
            {
                AddResult($"✅ 地理编码成功！{location.Address} -> ({location.Longitude}, {location.Latitude})", "success");
            }
            else
            {
                AddResult("❌ 地理编码失败", "error");
            }
        }
        catch (Exception ex)
        {
            AddResult($"❌ 地理编码异常: {ex.Message}", "error");
        }
    }

    protected async void btnTestRoute_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试路线规划功能...", "loading");
            
            var route = await AmapService.GetRouteAsync("116.397428,39.90923", "116.407428,39.91923");
            
            if (route != null)
            {
                AddResult($"✅ 路线规划成功！距离: {route.Distance}米, 时间: {route.Duration}秒, 过路费: {route.Tolls}元", "success");
            }
            else
            {
                AddResult("❌ 路线规划失败", "error");
            }
        }
        catch (Exception ex)
        {
            AddResult($"❌ 路线规划异常: {ex.Message}", "error");
        }
    }

    protected async void btnTestShops_Click(object sender, EventArgs e)
    {
        try
        {
            AddResult("正在测试维修店搜索功能...", "loading");
            
            // 测试数据库搜索
            var dbShops = ShopManager.GetNearbyShops(116.397428, 39.90923, 10);
            
            // 测试高德地图搜索
            var amapShops = await AmapService.SearchNearbyShopsAsync(116.397428, 39.90923, 5000);
            
            AddResult($"✅ 维修店搜索成功！数据库找到 {dbShops.Rows.Count} 个，高德地图找到 {amapShops.Count} 个", "success");
        }
        catch (Exception ex)
        {
            AddResult($"❌ 维修店搜索异常: {ex.Message}", "error");
        }
    }

    private void AddResult(string message, string type)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var cssClass = type;
        
        var html = $"<div class='test-result {cssClass}'>[{timestamp}] {message}</div>";
        litResults.Text += html;
    }
}
