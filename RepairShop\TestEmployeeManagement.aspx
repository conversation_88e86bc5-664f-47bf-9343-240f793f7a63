<%@ Page Title="测试员工管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="TestEmployeeManagement.aspx.cs" Inherits="RepairShop_TestEmployeeManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-users"></i> 员工管理功能测试</h2>
                <p class="text-muted">测试员工管理功能是否正常工作</p>
            </div>
        </div>

        <!-- 快速导航 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-link"></i> 快速导航</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="EmployeeManagement.aspx" class="btn btn-success btn-block mb-2">
                                    <i class="fas fa-users"></i> 员工管理
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="Appointments.aspx" class="btn btn-info btn-block mb-2">
                                    <i class="fas fa-calendar-check"></i> 预约管理
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="ServiceRecords.aspx" class="btn btn-warning btn-block mb-2">
                                    <i class="fas fa-wrench"></i> 维修记录
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="Dashboard.aspx" class="btn btn-secondary btn-block mb-2">
                                    <i class="fas fa-tachometer-alt"></i> 控制台
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 员工统计信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> 员工统计</h5>
                    </div>
                    <div class="card-body">
                        <asp:Label ID="lblEmployeeStats" runat="server" CssClass="h4"></asp:Label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-user-check"></i> 活跃员工</h5>
                    </div>
                    <div class="card-body">
                        <asp:Label ID="lblActiveEmployeeStats" runat="server" CssClass="h4"></asp:Label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 员工列表预览 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-list"></i> 员工列表预览</h5>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvEmployeesPreview" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" EmptyDataText="暂无员工记录">
                            <Columns>
                                <asp:BoundField DataField="EmployeeName" HeaderText="姓名" />
                                <asp:BoundField DataField="Position" HeaderText="职位" />
                                <asp:BoundField DataField="Phone" HeaderText="电话" />
                                <asp:BoundField DataField="Email" HeaderText="邮箱" />
                                <asp:BoundField DataField="HireDate" HeaderText="入职日期" DataFormatString="{0:yyyy-MM-dd}" />
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <span class='<%# Convert.ToBoolean(Eval("IsActive")) ? "badge badge-success" : "badge badge-secondary" %>'>
                                            <%# Convert.ToBoolean(Eval("IsActive")) ? "活跃" : "禁用" %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="row mt-3">
            <div class="col-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
            </div>
        </asp:Panel>
    </div>
</asp:Content>
