using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Admin_ChatManagement : System.Web.UI.Page
{
    private int currentUserID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查管理员权限
        if (!User.Identity.IsAuthenticated || Session["UserID"] == null || Session["UserType"]?.ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        currentUserID = Convert.ToInt32(Session["UserID"]);

        if (!IsPostBack)
        {
            LoadUsers();
            LoadChatRooms();
            LoadMessages();
            LoadRoomFilter();
        }
    }

    #region 用户管理

    /// <summary>
    /// 加载用户列表
    /// </summary>
    private void LoadUsers()
    {
        try
        {
            string searchKeyword = txtSearchUser.Text.Trim();
            string userTypeFilter = ddlUserTypeFilter.SelectedValue;

            string query = @"
                SELECT UserID, Username, Email, UserType, IsActive, CreatedDate
                FROM Users 
                WHERE UserType != 'Admin'";

            var parameters = new System.Collections.Generic.List<System.Data.SqlClient.SqlParameter>();

            if (!string.IsNullOrEmpty(searchKeyword))
            {
                query += " AND (Username LIKE @SearchKeyword OR Email LIKE @SearchKeyword)";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@SearchKeyword", "%" + searchKeyword + "%"));
            }

            if (!string.IsNullOrEmpty(userTypeFilter))
            {
                query += " AND UserType = @UserType";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@UserType", userTypeFilter));
            }

            query += " ORDER BY Username";

            DataTable users = DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
            gvUsers.DataSource = users;
            gvUsers.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载用户列表失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 搜索用户
    /// </summary>
    protected void btnSearchUser_Click(object sender, EventArgs e)
    {
        LoadUsers();
    }

    /// <summary>
    /// 用户类型筛选
    /// </summary>
    protected void ddlUserTypeFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        LoadUsers();
    }

    /// <summary>
    /// 用户操作事件
    /// </summary>
    protected void gvUsers_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            int userID = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "MuteUser":
                    ShowMuteDialog(userID);
                    break;
                case "UnmuteUser":
                    UnmuteUser(userID);
                    break;
                case "ViewMessages":
                    ViewUserMessages(userID);
                    break;
            }
        }
        catch (Exception ex)
        {
            ShowMessage("操作失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 显示禁言对话框
    /// </summary>
    private void ShowMuteDialog(int userID)
    {
        hfMuteUserID.Value = userID.ToString();
        ScriptManager.RegisterStartupScript(this, GetType(), "showMuteModal", "$('#muteUserModal').modal('show');", true);
    }

    /// <summary>
    /// 确认禁言
    /// </summary>
    protected void btnConfirmMute_Click(object sender, EventArgs e)
    {
        try
        {
            int userID = Convert.ToInt32(hfMuteUserID.Value);
            string muteReason = txtMuteReason.Text.Trim();
            int muteDurationHours = Convert.ToInt32(ddlMuteDuration.SelectedValue);
            
            DateTime? muteEndDate = null;
            if (muteDurationHours > 0)
            {
                muteEndDate = DateTime.Now.AddHours(muteDurationHours);
            }

            // 确定禁言范围
            int? roomID = null;
            if (rbPublicMute.Checked)
            {
                // 获取公共聊天室ID
                string getRoomQuery = "SELECT TOP 1 RoomID FROM ChatRooms WHERE RoomType = 'Public'";
                DataTable roomTable = DatabaseHelper.ExecuteQuery(getRoomQuery);
                if (roomTable.Rows.Count > 0)
                {
                    roomID = Convert.ToInt32(roomTable.Rows[0]["RoomID"]);
                }
            }

            bool success = ChatManager.MuteUser(userID, currentUserID, roomID, muteReason, muteEndDate);

            if (success)
            {
                ShowMessage("用户禁言成功。", "success");
                LoadUsers();
                ScriptManager.RegisterStartupScript(this, GetType(), "hideMuteModal", "$('#muteUserModal').modal('hide');", true);
                
                // 清空表单
                txtMuteReason.Text = "";
                rbGlobalMute.Checked = true;
                rbPublicMute.Checked = false;
                ddlMuteDuration.SelectedIndex = 0;
            }
            else
            {
                ShowMessage("禁言操作失败。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("禁言操作时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 解除禁言
    /// </summary>
    private void UnmuteUser(int userID)
    {
        try
        {
            bool success = ChatManager.UnmuteUser(userID);
            if (success)
            {
                ShowMessage("用户禁言已解除。", "success");
                LoadUsers();
            }
            else
            {
                ShowMessage("解除禁言失败。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("解除禁言时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 查看用户消息
    /// </summary>
    private void ViewUserMessages(int userID)
    {
        // 切换到消息管理标签页并筛选该用户的消息
        LoadMessages(userID);
        ScriptManager.RegisterStartupScript(this, GetType(), "switchTab", "$('#messages-tab').tab('show');", true);
    }

    #endregion

    #region 聊天室管理

    /// <summary>
    /// 加载聊天室列表
    /// </summary>
    private void LoadChatRooms()
    {
        try
        {
            string query = @"
                SELECT cr.RoomID, cr.RoomName, cr.RoomType, cr.Description, cr.CreatedDate,
                       (SELECT COUNT(*) FROM ChatMessages WHERE RoomID = cr.RoomID AND IsDeleted = 0) AS MessageCount,
                       (SELECT COUNT(*) FROM ChatRoomMembers WHERE RoomID = cr.RoomID AND IsActive = 1) AS MemberCount
                FROM ChatRooms cr
                WHERE cr.IsActive = 1
                ORDER BY cr.RoomType DESC, cr.CreatedDate DESC";

            DataTable rooms = DatabaseHelper.ExecuteQuery(query);
            gvChatRooms.DataSource = rooms;
            gvChatRooms.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载聊天室列表失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 聊天室操作事件
    /// </summary>
    protected void gvChatRooms_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            int roomID = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "ViewRoomMessages":
                    ViewRoomMessages(roomID);
                    break;
                case "ManageRoom":
                    ManageRoom(roomID);
                    break;
            }
        }
        catch (Exception ex)
        {
            ShowMessage("操作失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 查看聊天室消息
    /// </summary>
    private void ViewRoomMessages(int roomID)
    {
        ddlRoomFilter.SelectedValue = roomID.ToString();
        LoadMessages();
        ScriptManager.RegisterStartupScript(this, GetType(), "switchTab", "$('#messages-tab').tab('show');", true);
    }

    /// <summary>
    /// 管理聊天室
    /// </summary>
    private void ManageRoom(int roomID)
    {
        // 这里可以扩展更多聊天室管理功能
        ShowMessage("聊天室管理功能开发中...", "info");
    }

    #endregion

    #region 消息管理

    /// <summary>
    /// 加载聊天室筛选器
    /// </summary>
    private void LoadRoomFilter()
    {
        try
        {
            string query = "SELECT RoomID, RoomName FROM ChatRooms WHERE IsActive = 1 ORDER BY RoomType DESC, RoomName";
            DataTable rooms = DatabaseHelper.ExecuteQuery(query);

            ddlRoomFilter.Items.Clear();
            ddlRoomFilter.Items.Add(new ListItem("所有聊天室", ""));

            foreach (DataRow row in rooms.Rows)
            {
                ddlRoomFilter.Items.Add(new ListItem(row["RoomName"].ToString(), row["RoomID"].ToString()));
            }
        }
        catch (Exception ex)
        {
            ShowMessage("加载聊天室筛选器失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 加载消息列表
    /// </summary>
    private void LoadMessages(int? senderID = null)
    {
        try
        {
            string query = @"
                SELECT cm.MessageID, cm.RoomID, cm.SenderID, cm.MessageContent, cm.MessageType,
                       cm.SentDate, cm.IsDeleted, cm.DeletedBy, cm.DeletedDate,
                       u.Username AS SenderName, cr.RoomName
                FROM ChatMessages cm
                INNER JOIN Users u ON cm.SenderID = u.UserID
                INNER JOIN ChatRooms cr ON cm.RoomID = cr.RoomID
                WHERE 1=1";

            var parameters = new System.Collections.Generic.List<System.Data.SqlClient.SqlParameter>();

            // 聊天室筛选
            if (!string.IsNullOrEmpty(ddlRoomFilter.SelectedValue))
            {
                query += " AND cm.RoomID = @RoomID";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@RoomID", Convert.ToInt32(ddlRoomFilter.SelectedValue)));
            }

            // 发送者筛选
            if (senderID.HasValue)
            {
                query += " AND cm.SenderID = @SenderID";
                parameters.Add(new System.Data.SqlClient.SqlParameter("@SenderID", senderID.Value));
            }

            // 是否显示已删除消息
            if (!chkShowDeletedMessages.Checked)
            {
                query += " AND cm.IsDeleted = 0";
            }

            query += " ORDER BY cm.SentDate DESC";

            DataTable messages = DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
            gvMessages.DataSource = messages;
            gvMessages.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载消息列表失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 聊天室筛选变更
    /// </summary>
    protected void ddlRoomFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        LoadMessages();
    }

    /// <summary>
    /// 显示已删除消息选项变更
    /// </summary>
    protected void chkShowDeletedMessages_CheckedChanged(object sender, EventArgs e)
    {
        LoadMessages();
    }

    /// <summary>
    /// 消息分页
    /// </summary>
    protected void gvMessages_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvMessages.PageIndex = e.NewPageIndex;
        LoadMessages();
    }

    /// <summary>
    /// 消息操作事件
    /// </summary>
    protected void gvMessages_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "DeleteMessage")
        {
            try
            {
                int messageID = Convert.ToInt32(e.CommandArgument);
                bool success = ChatManager.DeleteMessage(messageID, currentUserID);

                if (success)
                {
                    ShowMessage("消息已删除。", "success");
                    LoadMessages();
                }
                else
                {
                    ShowMessage("删除消息失败。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("删除消息时出错：" + ex.Message, "danger");
            }
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 获取用户类型显示名称
    /// </summary>
    protected string GetUserTypeDisplayName(string userType)
    {
        switch (userType)
        {
            case "CarOwner": return "车主";
            case "RepairShop": return "维修店";
            case "Admin": return "管理员";
            default: return userType;
        }
    }

    /// <summary>
    /// 获取用户类型徽章样式
    /// </summary>
    protected string GetUserTypeBadgeClass(string userType)
    {
        switch (userType)
        {
            case "CarOwner": return "badge badge-primary";
            case "RepairShop": return "badge badge-success";
            case "Admin": return "badge badge-danger";
            default: return "badge badge-secondary";
        }
    }

    /// <summary>
    /// 获取用户禁言状态显示
    /// </summary>
    protected string GetMuteStatusDisplay(int userID)
    {
        try
        {
            bool isMuted = ChatManager.IsUserMuted(userID);
            if (isMuted)
            {
                return "<span class='badge badge-warning'>已禁言</span>";
            }
            else
            {
                return "<span class='badge badge-success'>正常</span>";
            }
        }
        catch
        {
            return "<span class='badge badge-secondary'>未知</span>";
        }
    }

    /// <summary>
    /// 显示消息
    /// </summary>
    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = "alert alert-" + type;
        pnlMessage.Visible = true;
    }

    #endregion
}
