using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data;

/// <summary>
/// 路线优化器，提供智能路线规划功能
/// </summary>
public class RouteOptimizer
{
    /// <summary>
    /// 获取最优维修店推荐
    /// </summary>
    /// <param name="userLongitude">用户经度</param>
    /// <param name="userLatitude">用户纬度</param>
    /// <param name="preferences">用户偏好</param>
    /// <returns>优化后的维修店推荐</returns>
    public static async Task<List<OptimizedShopRecommendation>> GetOptimizedShopRecommendationsAsync(
        double userLongitude, double userLatitude, RoutePreferences preferences = null)
    {
        try
        {
            preferences = preferences ?? new RoutePreferences();
            
            // 1. 获取附近的维修店
            var nearbyShops = ShopManager.GetNearbyShops(userLongitude, userLatitude, preferences.MaxDistanceKm);
            var recommendations = new List<OptimizedShopRecommendation>();

            foreach (DataRow shop in nearbyShops.Rows)
            {
                if (shop["Longitude"] == DBNull.Value || shop["Latitude"] == DBNull.Value)
                    continue;

                var shopLon = Convert.ToDouble(shop["Longitude"]);
                var shopLat = Convert.ToDouble(shop["Latitude"]);
                var shopID = Convert.ToInt32(shop["ShopID"]);

                // 2. 计算路线信息
                var route = await AmapService.GetRouteAsync(
                    $"{userLongitude},{userLatitude}",
                    $"{shopLon},{shopLat}",
                    preferences.RouteStrategy);

                if (route == null) continue;

                // 3. 检查营业时间
                var businessHours = shop["BusinessHours"]?.ToString();
                var isOpen = IsShopOpen(businessHours, preferences.PreferredArrivalTime);

                // 4. 计算综合评分
                var score = CalculateShopScore(shop, route, isOpen, preferences);

                recommendations.Add(new OptimizedShopRecommendation
                {
                    ShopID = shopID,
                    Name = shop["ShopName"].ToString(),
                    Address = shop["Address"].ToString(),
                    Rating = Convert.ToDouble(shop["Rating"]),
                    Distance = route.Distance,
                    Duration = route.Duration,
                    Tolls = route.Tolls,
                    TrafficLights = route.TrafficLights,
                    IsOpen = isOpen,
                    BusinessHours = businessHours,
                    Longitude = shopLon,
                    Latitude = shopLat,
                    OptimizationScore = score,
                    RouteInfo = route
                });
            }

            // 5. 按优化评分排序
            return recommendations.OrderByDescending(r => r.OptimizationScore).ToList();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路线优化错误: {ex.Message}");
            return new List<OptimizedShopRecommendation>();
        }
    }

    /// <summary>
    /// 检查维修店是否营业
    /// </summary>
    /// <param name="businessHours">营业时间</param>
    /// <param name="arrivalTime">预计到达时间</param>
    /// <returns>是否营业</returns>
    private static bool IsShopOpen(string businessHours, DateTime? arrivalTime = null)
    {
        if (string.IsNullOrEmpty(businessHours))
            return true; // 如果没有营业时间信息，假设营业

        var checkTime = arrivalTime ?? DateTime.Now;
        
        try
        {
            // 简单的营业时间解析（格式：09:00-18:00）
            if (businessHours.Contains("-"))
            {
                var parts = businessHours.Split('-');
                if (parts.Length == 2)
                {
                    var openTime = TimeSpan.Parse(parts[0].Trim());
                    var closeTime = TimeSpan.Parse(parts[1].Trim());
                    var currentTime = checkTime.TimeOfDay;

                    return currentTime >= openTime && currentTime <= closeTime;
                }
            }
        }
        catch
        {
            // 解析失败，假设营业
        }

        return true;
    }

    /// <summary>
    /// 计算维修店综合评分
    /// </summary>
    /// <param name="shop">维修店信息</param>
    /// <param name="route">路线信息</param>
    /// <param name="isOpen">是否营业</param>
    /// <param name="preferences">用户偏好</param>
    /// <returns>综合评分</returns>
    private static double CalculateShopScore(DataRow shop, RouteInfo route, bool isOpen, RoutePreferences preferences)
    {
        double score = 0;

        // 1. 距离评分（距离越近分数越高）
        var distanceScore = Math.Max(0, 100 - (route.Distance / 1000.0 * preferences.DistanceWeight));
        score += distanceScore * 0.3;

        // 2. 时间评分（时间越短分数越高）
        var timeScore = Math.Max(0, 100 - (route.Duration / 60.0 * preferences.TimeWeight));
        score += timeScore * 0.25;

        // 3. 评分评分
        var rating = Convert.ToDouble(shop["Rating"]);
        var ratingScore = rating * 20; // 5分制转100分制
        score += ratingScore * 0.25;

        // 4. 营业状态评分
        if (isOpen)
        {
            score += 20;
        }
        else
        {
            score -= 30; // 不营业扣分
        }

        // 5. 过路费评分（费用越低分数越高）
        var tollScore = Math.Max(0, 100 - route.Tolls * 2);
        score += tollScore * 0.1;

        // 6. 红绿灯评分（红绿灯越少分数越高）
        var trafficLightScore = Math.Max(0, 100 - route.TrafficLights * 5);
        score += trafficLightScore * 0.1;

        return Math.Max(0, score);
    }

    /// <summary>
    /// 获取最佳路线建议
    /// </summary>
    /// <param name="userLocation">用户位置</param>
    /// <param name="targetShopID">目标维修店ID</param>
    /// <param name="preferences">路线偏好</param>
    /// <returns>路线建议</returns>
    public static async Task<RouteSuggestion> GetBestRouteAsync(
        LocationInfo userLocation, int targetShopID, RoutePreferences preferences = null)
    {
        try
        {
            preferences = preferences ?? new RoutePreferences();

            // 获取维修店信息
            var shopData = ShopManager.GetShopWithLocation(targetShopID);
            if (shopData.Rows.Count == 0)
                return null;

            var shop = shopData.Rows[0];
            var shopLon = Convert.ToDouble(shop["Longitude"]);
            var shopLat = Convert.ToDouble(shop["Latitude"]);

            // 获取多种路线策略的结果
            var routes = new List<RouteInfo>();
            var strategies = new[] { 0, 1, 2, 3 }; // 速度优先、费用优先、距离优先、不走高速

            foreach (var strategy in strategies)
            {
                var route = await AmapService.GetRouteAsync(
                    $"{userLocation.Longitude},{userLocation.Latitude}",
                    $"{shopLon},{shopLat}",
                    strategy);

                if (route != null)
                {
                    route.Strategy = strategy;
                    routes.Add(route);
                }
            }

            if (routes.Count == 0)
                return null;

            // 根据偏好选择最佳路线
            var bestRoute = SelectBestRoute(routes, preferences);

            // 检查营业时间
            var businessHours = shop["BusinessHours"]?.ToString();
            var arrivalTime = DateTime.Now.AddSeconds(bestRoute.Duration);
            var isOpen = IsShopOpen(businessHours, arrivalTime);

            return new RouteSuggestion
            {
                ShopInfo = new RecommendedShop
                {
                    ShopID = targetShopID,
                    Name = shop["ShopName"].ToString(),
                    Address = shop["Address"].ToString(),
                    Rating = Convert.ToDouble(shop["Rating"]),
                    BusinessHours = businessHours,
                    Longitude = shopLon,
                    Latitude = shopLat
                },
                BestRoute = bestRoute,
                AlternativeRoutes = routes.Where(r => r != bestRoute).ToList(),
                EstimatedArrivalTime = arrivalTime,
                IsShopOpen = isOpen,
                Recommendations = GenerateRouteRecommendations(bestRoute, isOpen, businessHours)
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取最佳路线错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 选择最佳路线
    /// </summary>
    private static RouteInfo SelectBestRoute(List<RouteInfo> routes, RoutePreferences preferences)
    {
        return routes.OrderBy(r => 
            r.Distance * preferences.DistanceWeight +
            r.Duration * preferences.TimeWeight +
            r.Tolls * preferences.CostWeight
        ).First();
    }

    /// <summary>
    /// 生成路线建议
    /// </summary>
    private static List<string> GenerateRouteRecommendations(RouteInfo route, bool isOpen, string businessHours)
    {
        var recommendations = new List<string>();

        if (!isOpen)
        {
            recommendations.Add($"⚠️ 维修店当前不营业，营业时间：{businessHours}");
        }

        if (route.Duration > 3600) // 超过1小时
        {
            recommendations.Add("🕐 路程较远，建议提前联系维修店确认服务时间");
        }

        if (route.Tolls > 20)
        {
            recommendations.Add($"💰 此路线过路费较高（{route.Tolls}元），可考虑其他路线");
        }

        if (route.TrafficLights > 10)
        {
            recommendations.Add("🚦 此路线红绿灯较多，请预留充足时间");
        }

        return recommendations;
    }
}

#region 数据模型

/// <summary>
/// 路线偏好设置
/// </summary>
public class RoutePreferences
{
    public double MaxDistanceKm { get; set; } = 20; // 最大搜索距离
    public int RouteStrategy { get; set; } = 0; // 路线策略：0-速度优先，1-费用优先，2-距离优先，3-不走高速
    public double DistanceWeight { get; set; } = 1.0; // 距离权重
    public double TimeWeight { get; set; } = 1.0; // 时间权重
    public double CostWeight { get; set; } = 0.5; // 费用权重
    public DateTime? PreferredArrivalTime { get; set; } // 期望到达时间
}

/// <summary>
/// 优化后的维修店推荐
/// </summary>
public class OptimizedShopRecommendation : RecommendedShop
{
    public int Duration { get; set; } // 行驶时间（秒）
    public int Tolls { get; set; } // 过路费
    public int TrafficLights { get; set; } // 红绿灯数量
    public bool IsOpen { get; set; } // 是否营业
    public double OptimizationScore { get; set; } // 优化评分
    public RouteInfo RouteInfo { get; set; } // 详细路线信息
}

/// <summary>
/// 路线建议
/// </summary>
public class RouteSuggestion
{
    public RecommendedShop ShopInfo { get; set; }
    public RouteInfo BestRoute { get; set; }
    public List<RouteInfo> AlternativeRoutes { get; set; }
    public DateTime EstimatedArrivalTime { get; set; }
    public bool IsShopOpen { get; set; }
    public List<string> Recommendations { get; set; }
}

#endregion
