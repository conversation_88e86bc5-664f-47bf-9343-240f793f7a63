using System;
using System.Data;
using System.Web.UI;

public partial class RepairShop_TestEmployeeManagement : System.Web.UI.Page
{
    private int userID;
    private int shopID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户登录和权限
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        shopID = ShopManager.GetShopIDByUserID(userID);

        if (shopID == -1)
        {
            ShowMessage("无法获取维修店信息，请联系管理员。", "danger");
            return;
        }

        if (!IsPostBack)
        {
            LoadEmployeeStatistics();
            LoadEmployeePreview();
        }
    }

    /// <summary>
    /// 加载员工统计信息
    /// </summary>
    private void LoadEmployeeStatistics()
    {
        try
        {
            // 获取所有员工
            DataTable allEmployees = ShopManager.GetEmployees(shopID, true);
            int totalEmployees = allEmployees.Rows.Count;

            // 获取活跃员工
            DataTable activeEmployees = ShopManager.GetActiveEmployees(shopID);
            int activeEmployeeCount = activeEmployees.Rows.Count;

            // 计算禁用员工数量
            int inactiveEmployeeCount = totalEmployees - activeEmployeeCount;

            lblEmployeeStats.Text = $"总员工数: {totalEmployees} 人";
            lblActiveEmployeeStats.Text = $"活跃员工: {activeEmployeeCount} 人 | 禁用员工: {inactiveEmployeeCount} 人";

            if (totalEmployees == 0)
            {
                ShowMessage("您还没有添加任何员工。请点击上方的"员工管理"按钮开始添加员工。", "info");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("加载员工统计信息时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 加载员工列表预览
    /// </summary>
    private void LoadEmployeePreview()
    {
        try
        {
            DataTable employeesTable = ShopManager.GetEmployees(shopID, true);
            gvEmployeesPreview.DataSource = employeesTable;
            gvEmployeesPreview.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载员工列表时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 显示消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="type">消息类型</param>
    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = "alert alert-" + type;
        pnlMessage.Visible = true;
    }
}
