using System;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// 零件管理类
/// </summary>
public class PartsManager
{
    /// <summary>
    /// 获取指定维修店的所有零件
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>零件列表</returns>
    public static DataTable GetPartsByShopID(int shopID)
    {
        string query = @"SELECT PartID, PartName, PartNumber, Price, StockQuantity, Description, IsActive
                        FROM Parts 
                        WHERE ShopID = @ShopID AND IsActive = 1
                        ORDER BY PartName";

        SqlParameter[] parameters = 
        {
            new SqlParameter("@ShopID", shopID)
        };

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 添加新零件
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="partName">零件名称</param>
    /// <param name="partNumber">零件编号</param>
    /// <param name="price">价格</param>
    /// <param name="stockQuantity">库存数量</param>
    /// <param name="description">描述</param>
    /// <returns>新增零件的ID，失败返回-1</returns>
    public static int AddPart(int shopID, string partName, string partNumber, decimal price, int stockQuantity, string description)
    {
        try
        {
            string query = @"INSERT INTO Parts (ShopID, PartName, PartNumber, Price, StockQuantity, Description, IsActive, CreatedDate)
                            VALUES (@ShopID, @PartName, @PartNumber, @Price, @StockQuantity, @Description, 1, GETDATE());
                            SELECT SCOPE_IDENTITY();";

            SqlParameter[] parameters = 
            {
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@PartName", partName),
                new SqlParameter("@PartNumber", partNumber ?? (object)DBNull.Value),
                new SqlParameter("@Price", price),
                new SqlParameter("@StockQuantity", stockQuantity),
                new SqlParameter("@Description", description ?? (object)DBNull.Value)
            };

            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            return result != null ? Convert.ToInt32(result) : -1;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加零件时出错: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 更新零件信息
    /// </summary>
    /// <param name="partID">零件ID</param>
    /// <param name="partName">零件名称</param>
    /// <param name="partNumber">零件编号</param>
    /// <param name="price">价格</param>
    /// <param name="stockQuantity">库存数量</param>
    /// <param name="description">描述</param>
    /// <returns>是否成功</returns>
    public static bool UpdatePart(int partID, string partName, string partNumber, decimal price, int stockQuantity, string description)
    {
        try
        {
            string query = @"UPDATE Parts 
                            SET PartName = @PartName, PartNumber = @PartNumber, Price = @Price, 
                                StockQuantity = @StockQuantity, Description = @Description, ModifiedDate = GETDATE()
                            WHERE PartID = @PartID";

            SqlParameter[] parameters = 
            {
                new SqlParameter("@PartID", partID),
                new SqlParameter("@PartName", partName),
                new SqlParameter("@PartNumber", partNumber ?? (object)DBNull.Value),
                new SqlParameter("@Price", price),
                new SqlParameter("@StockQuantity", stockQuantity),
                new SqlParameter("@Description", description ?? (object)DBNull.Value)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新零件时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 删除零件（软删除）
    /// </summary>
    /// <param name="partID">零件ID</param>
    /// <returns>是否成功</returns>
    public static bool DeletePart(int partID)
    {
        try
        {
            string query = @"UPDATE Parts SET IsActive = 0, ModifiedDate = GETDATE() WHERE PartID = @PartID";

            SqlParameter[] parameters = 
            {
                new SqlParameter("@PartID", partID)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除零件时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取零件详情
    /// </summary>
    /// <param name="partID">零件ID</param>
    /// <returns>零件信息</returns>
    public static DataTable GetPartByID(int partID)
    {
        string query = @"SELECT PartID, ShopID, PartName, PartNumber, Price, StockQuantity, Description, IsActive
                        FROM Parts 
                        WHERE PartID = @PartID";

        SqlParameter[] parameters = 
        {
            new SqlParameter("@PartID", partID)
        };

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 检查零件编号是否已存在
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="partNumber">零件编号</param>
    /// <param name="excludePartID">排除的零件ID（用于更新时检查）</param>
    /// <returns>是否存在</returns>
    public static bool IsPartNumberExists(int shopID, string partNumber, int excludePartID = 0)
    {
        if (string.IsNullOrEmpty(partNumber))
            return false;

        try
        {
            string query = @"SELECT COUNT(*) FROM Parts 
                            WHERE ShopID = @ShopID AND PartNumber = @PartNumber AND IsActive = 1";
            
            SqlParameter[] parameters = 
            {
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@PartNumber", partNumber)
            };

            if (excludePartID > 0)
            {
                query += " AND PartID != @ExcludePartID";
                Array.Resize(ref parameters, parameters.Length + 1);
                parameters[parameters.Length - 1] = new SqlParameter("@ExcludePartID", excludePartID);
            }

            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            return Convert.ToInt32(result) > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"检查零件编号时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 更新零件库存
    /// </summary>
    /// <param name="partID">零件ID</param>
    /// <param name="quantity">使用数量</param>
    /// <returns>是否成功</returns>
    public static bool UpdatePartStock(int partID, int quantity)
    {
        try
        {
            string query = @"UPDATE Parts 
                            SET StockQuantity = StockQuantity - @Quantity, ModifiedDate = GETDATE()
                            WHERE PartID = @PartID AND StockQuantity >= @Quantity";

            SqlParameter[] parameters = 
            {
                new SqlParameter("@PartID", partID),
                new SqlParameter("@Quantity", quantity)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新零件库存时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 创建零件表的SQL脚本
    /// </summary>
    /// <returns>创建表的SQL脚本</returns>
    public static string GetCreateTableScript()
    {
        return @"
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Parts' AND xtype='U')
            BEGIN
                CREATE TABLE Parts (
                    PartID int IDENTITY(1,1) PRIMARY KEY,
                    ShopID int NOT NULL,
                    PartName nvarchar(100) NOT NULL,
                    PartNumber nvarchar(50),
                    Price decimal(10,2) NOT NULL,
                    StockQuantity int NOT NULL DEFAULT 0,
                    Description nvarchar(500),
                    IsActive bit NOT NULL DEFAULT 1,
                    CreatedDate datetime NOT NULL DEFAULT GETDATE(),
                    ModifiedDate datetime,
                    FOREIGN KEY (ShopID) REFERENCES RepairShops(ShopID)
                );
                
                CREATE INDEX IX_Parts_ShopID ON Parts(ShopID);
                CREATE INDEX IX_Parts_PartNumber ON Parts(PartNumber);
            END
            
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ServiceRecordParts' AND xtype='U')
            BEGIN
                CREATE TABLE ServiceRecordParts (
                    RecordPartID int IDENTITY(1,1) PRIMARY KEY,
                    RecordID int NOT NULL,
                    PartID int NOT NULL,
                    Quantity int NOT NULL DEFAULT 1,
                    UnitPrice decimal(10,2) NOT NULL,
                    TotalPrice AS (Quantity * UnitPrice),
                    CreatedDate datetime NOT NULL DEFAULT GETDATE(),
                    FOREIGN KEY (RecordID) REFERENCES ServiceRecords(RecordID),
                    FOREIGN KEY (PartID) REFERENCES Parts(PartID)
                );
                
                CREATE INDEX IX_ServiceRecordParts_RecordID ON ServiceRecordParts(RecordID);
                CREATE INDEX IX_ServiceRecordParts_PartID ON ServiceRecordParts(PartID);
            END";
    }
}