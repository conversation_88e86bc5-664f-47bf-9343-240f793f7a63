<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TestAIAssistant.aspx.cs" Inherits="AIAssistant_TestAIAssistant" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>AI助手功能测试</title>
    <meta charset="utf-8">
    <script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section h3 { margin-top: 0; color: #333; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .test-input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
        .test-button { 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        .test-button:hover { background: #0056b3; }
        .test-result { 
            margin-top: 15px; 
            padding: 15px; 
            background: #f8f9fa; 
            border-radius: 4px; 
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .loading { border-left-color: #ffc107; background: #fff3cd; }
        .quick-tests { display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0; }
        .quick-test-btn { 
            padding: 8px 12px; 
            background: #6c757d; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 12px;
        }
        .quick-test-btn:hover { background: #545b62; }
        .status-indicator { 
            display: inline-block; 
            width: 10px; 
            height: 10px; 
            border-radius: 50%; 
            margin-right: 5px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-loading { background: #ffc107; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="test-container">
            <h1>🤖 AI智能助手功能测试</h1>
            
            <div class="test-section">
                <h3>📊 系统状态检查</h3>
                <div class="test-grid">
                    <div>
                        <button type="button" class="test-button" onclick="checkAIService()">测试AI服务连接</button>
                        <div id="aiServiceStatus">
                            <span class="status-indicator status-loading"></span>等待测试...
                        </div>
                    </div>
                    <div>
                        <button type="button" class="test-button" onclick="checkAmapService()">测试高德地图API</button>
                        <div id="amapServiceStatus">
                            <span class="status-indicator status-loading"></span>等待测试...
                        </div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>💬 AI对话测试</h3>
                <div>
                    <input type="text" id="testMessage" class="test-input" placeholder="输入测试消息..." />
                    <input type="text" id="testLocation" class="test-input" placeholder="用户位置（可选，格式：经度,纬度）" />
                    <button type="button" class="test-button" onclick="testAIChat()">发送测试消息</button>
                </div>
                
                <div class="quick-tests">
                    <span>快速测试：</span>
                    <button type="button" class="quick-test-btn" onclick="quickTest('我想找最近的维修店', '116.397428,39.90923')">找维修店</button>
                    <button type="button" class="quick-test-btn" onclick="quickTest('我的车发动机有异响', '')">故障诊断</button>
                    <button type="button" class="quick-test-btn" onclick="quickTest('保养需要多少钱', '')">价格咨询</button>
                    <button type="button" class="quick-test-btn" onclick="quickTest('什么时候需要保养', '')">保养建议</button>
                    <button type="button" class="quick-test-btn" onclick="quickTest('从北京天安门到最近的4S店怎么走', '116.397428,39.90923')">路线规划</button>
                </div>
                
                <div id="chatTestResult" class="test-result"></div>
            </div>

            <div class="test-section">
                <h3>🗺️ 地图服务测试</h3>
                <div class="test-grid">
                    <div>
                        <h4>地理编码测试</h4>
                        <input type="text" id="geocodeAddress" class="test-input" placeholder="输入地址..." value="北京市朝阳区" />
                        <button type="button" class="test-button" onclick="testGeocode()">地理编码</button>
                        <div id="geocodeResult" class="test-result"></div>
                    </div>
                    <div>
                        <h4>附近维修店搜索</h4>
                        <input type="text" id="searchLongitude" class="test-input" placeholder="经度" value="116.397428" />
                        <input type="text" id="searchLatitude" class="test-input" placeholder="纬度" value="39.90923" />
                        <button type="button" class="test-button" onclick="testNearbySearch()">搜索附近维修店</button>
                        <div id="searchResult" class="test-result"></div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>🛣️ 路线规划测试</h3>
                <div>
                    <input type="text" id="routeOrigin" class="test-input" placeholder="起点（经度,纬度）" value="116.397428,39.90923" />
                    <input type="text" id="routeDestination" class="test-input" placeholder="终点（经度,纬度）" value="116.407428,39.91923" />
                    <select id="routeStrategy" class="test-input">
                        <option value="0">速度优先</option>
                        <option value="1">费用优先</option>
                        <option value="2">距离优先</option>
                        <option value="3">不走高速</option>
                    </select>
                    <button type="button" class="test-button" onclick="testRouteOptimization()">路线规划</button>
                </div>
                <div id="routeResult" class="test-result"></div>
            </div>

            <div class="test-section">
                <h3>📈 性能测试</h3>
                <div>
                    <button type="button" class="test-button" onclick="runPerformanceTest()">运行性能测试</button>
                    <button type="button" class="test-button" onclick="runStressTest()">压力测试（10次并发）</button>
                </div>
                <div id="performanceResult" class="test-result"></div>
            </div>
        </div>
    </form>

    <script type="text/javascript">
        // 检查AI服务
        function checkAIService() {
            updateStatus('aiServiceStatus', 'loading', '正在检查AI服务...');
            
            $.ajax({
                type: "POST",
                url: "TestAIAssistant.aspx/TestAIConnection",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var result = JSON.parse(response.d);
                    if (result.success) {
                        updateStatus('aiServiceStatus', 'success', 'AI服务连接正常');
                    } else {
                        updateStatus('aiServiceStatus', 'error', 'AI服务连接失败: ' + result.message);
                    }
                },
                error: function() {
                    updateStatus('aiServiceStatus', 'error', 'AI服务连接失败');
                }
            });
        }

        // 检查高德地图服务
        function checkAmapService() {
            updateStatus('amapServiceStatus', 'loading', '正在检查高德地图API...');
            
            $.ajax({
                type: "POST",
                url: "TestAIAssistant.aspx/TestAmapConnection",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var result = JSON.parse(response.d);
                    if (result.success) {
                        updateStatus('amapServiceStatus', 'success', '高德地图API连接正常');
                    } else {
                        updateStatus('amapServiceStatus', 'error', '高德地图API连接失败: ' + result.message);
                    }
                },
                error: function() {
                    updateStatus('amapServiceStatus', 'error', '高德地图API连接失败');
                }
            });
        }

        // 测试AI对话
        function testAIChat() {
            var message = document.getElementById('testMessage').value;
            var location = document.getElementById('testLocation').value;
            
            if (!message.trim()) {
                alert('请输入测试消息');
                return;
            }
            
            showResult('chatTestResult', '正在处理消息...', 'loading');
            var startTime = Date.now();
            
            $.ajax({
                type: "POST",
                url: "ChatAssistant.aspx/ProcessMessage",
                data: JSON.stringify({ 
                    message: message,
                    userLocation: location || null
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var endTime = Date.now();
                    var responseTime = endTime - startTime;
                    
                    var result = JSON.parse(response.d);
                    var output = `响应时间: ${responseTime}ms\n\n`;
                    output += `意图识别: ${result.Intent}\n`;
                    output += `包含位置数据: ${result.HasLocationData}\n\n`;
                    output += `AI回复:\n${result.Message}\n\n`;
                    
                    if (result.RecommendedShops && result.RecommendedShops.length > 0) {
                        output += `推荐维修店 (${result.RecommendedShops.length}个):\n`;
                        result.RecommendedShops.forEach(function(shop, index) {
                            output += `${index + 1}. ${shop.Name} - ${shop.Address}\n`;
                        });
                        output += '\n';
                    }
                    
                    if (result.RouteInfo) {
                        output += `路线信息:\n`;
                        output += `距离: ${(result.RouteInfo.Distance / 1000).toFixed(1)}公里\n`;
                        output += `时间: ${Math.round(result.RouteInfo.Duration / 60)}分钟\n`;
                        output += `过路费: ${result.RouteInfo.Tolls}元\n`;
                    }
                    
                    showResult('chatTestResult', output, 'success');
                },
                error: function(xhr, status, error) {
                    showResult('chatTestResult', `请求失败: ${error}`, 'error');
                }
            });
        }

        // 快速测试
        function quickTest(message, location) {
            document.getElementById('testMessage').value = message;
            document.getElementById('testLocation').value = location;
            testAIChat();
        }

        // 测试地理编码
        function testGeocode() {
            var address = document.getElementById('geocodeAddress').value;
            if (!address.trim()) {
                alert('请输入地址');
                return;
            }
            
            showResult('geocodeResult', '正在地理编码...', 'loading');
            
            $.ajax({
                type: "POST",
                url: "TestAIAssistant.aspx/TestGeocode",
                data: JSON.stringify({ address: address }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var result = JSON.parse(response.d);
                    if (result.success) {
                        var output = `地址: ${result.location.Address}\n`;
                        output += `经度: ${result.location.Longitude}\n`;
                        output += `纬度: ${result.location.Latitude}\n`;
                        output += `城市: ${result.location.City}\n`;
                        output += `区域: ${result.location.District}`;
                        showResult('geocodeResult', output, 'success');
                    } else {
                        showResult('geocodeResult', `地理编码失败: ${result.message}`, 'error');
                    }
                },
                error: function() {
                    showResult('geocodeResult', '地理编码请求失败', 'error');
                }
            });
        }

        // 测试附近搜索
        function testNearbySearch() {
            var longitude = parseFloat(document.getElementById('searchLongitude').value);
            var latitude = parseFloat(document.getElementById('searchLatitude').value);
            
            if (isNaN(longitude) || isNaN(latitude)) {
                alert('请输入有效的经纬度');
                return;
            }
            
            showResult('searchResult', '正在搜索附近维修店...', 'loading');
            
            $.ajax({
                type: "POST",
                url: "ChatAssistant.aspx/SearchNearbyShops",
                data: JSON.stringify({ 
                    longitude: longitude,
                    latitude: latitude,
                    radius: 10
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var result = JSON.parse(response.d);
                    if (result.success) {
                        var output = `找到 ${result.shops.length} 个维修店:\n\n`;
                        result.shops.forEach(function(shop, index) {
                            output += `${index + 1}. ${shop.Name}\n`;
                            output += `   地址: ${shop.Address}\n`;
                            output += `   距离: ${shop.Distance}米\n`;
                            output += `   评分: ${shop.Rating}分\n\n`;
                        });
                        showResult('searchResult', output, 'success');
                    } else {
                        showResult('searchResult', `搜索失败: ${result.message}`, 'error');
                    }
                },
                error: function() {
                    showResult('searchResult', '搜索请求失败', 'error');
                }
            });
        }

        // 测试路线优化
        function testRouteOptimization() {
            var origin = document.getElementById('routeOrigin').value;
            var destination = document.getElementById('routeDestination').value;
            var strategy = parseInt(document.getElementById('routeStrategy').value);
            
            if (!origin.trim() || !destination.trim()) {
                alert('请输入起点和终点');
                return;
            }
            
            showResult('routeResult', '正在规划路线...', 'loading');
            
            $.ajax({
                type: "POST",
                url: "ChatAssistant.aspx/GetRoute",
                data: JSON.stringify({ 
                    origin: origin,
                    destination: destination,
                    strategy: strategy
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    var result = JSON.parse(response.d);
                    if (result.success) {
                        var route = result.route;
                        var output = `路线规划成功:\n\n`;
                        output += `距离: ${(route.Distance / 1000).toFixed(1)}公里\n`;
                        output += `时间: ${Math.round(route.Duration / 60)}分钟\n`;
                        output += `过路费: ${route.Tolls}元\n`;
                        output += `红绿灯: ${route.TrafficLights}个\n\n`;
                        
                        if (route.Steps && route.Steps.length > 0) {
                            output += `导航步骤:\n`;
                            route.Steps.slice(0, 5).forEach(function(step, index) {
                                output += `${index + 1}. ${step.Instruction}\n`;
                            });
                            if (route.Steps.length > 5) {
                                output += `... 还有 ${route.Steps.length - 5} 个步骤\n`;
                            }
                        }
                        
                        showResult('routeResult', output, 'success');
                    } else {
                        showResult('routeResult', `路线规划失败: ${result.message}`, 'error');
                    }
                },
                error: function() {
                    showResult('routeResult', '路线规划请求失败', 'error');
                }
            });
        }

        // 性能测试
        function runPerformanceTest() {
            showResult('performanceResult', '正在运行性能测试...', 'loading');
            
            var tests = [
                { name: 'AI对话', func: () => testMessage('你好') },
                { name: '地理编码', func: () => testGeocode('北京市') },
                { name: '附近搜索', func: () => testNearbyShops(116.397428, 39.90923) },
                { name: '路线规划', func: () => testRoute('116.397428,39.90923', '116.407428,39.91923') }
            ];
            
            var results = [];
            var completed = 0;
            
            tests.forEach(function(test) {
                var startTime = Date.now();
                test.func().then(function() {
                    var endTime = Date.now();
                    results.push(`${test.name}: ${endTime - startTime}ms`);
                    completed++;
                    
                    if (completed === tests.length) {
                        var output = '性能测试结果:\n\n' + results.join('\n');
                        showResult('performanceResult', output, 'success');
                    }
                }).catch(function() {
                    results.push(`${test.name}: 失败`);
                    completed++;
                    
                    if (completed === tests.length) {
                        var output = '性能测试结果:\n\n' + results.join('\n');
                        showResult('performanceResult', output, 'success');
                    }
                });
            });
        }

        // 压力测试
        function runStressTest() {
            showResult('performanceResult', '正在运行压力测试...', 'loading');
            
            var promises = [];
            var startTime = Date.now();
            
            for (var i = 0; i < 10; i++) {
                promises.push(testMessage('压力测试消息 ' + (i + 1)));
            }
            
            Promise.all(promises).then(function() {
                var endTime = Date.now();
                var output = `压力测试完成:\n`;
                output += `10个并发请求总时间: ${endTime - startTime}ms\n`;
                output += `平均响应时间: ${(endTime - startTime) / 10}ms`;
                showResult('performanceResult', output, 'success');
            }).catch(function() {
                showResult('performanceResult', '压力测试失败', 'error');
            });
        }

        // 辅助函数
        function updateStatus(elementId, status, message) {
            var element = document.getElementById(elementId);
            var statusClass = 'status-' + status;
            element.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
        }

        function showResult(elementId, content, type) {
            var element = document.getElementById(elementId);
            element.className = 'test-result ' + type;
            element.textContent = content;
        }

        // 测试辅助函数
        function testMessage(message) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    type: "POST",
                    url: "ChatAssistant.aspx/ProcessMessage",
                    data: JSON.stringify({ message: message, userLocation: null }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: resolve,
                    error: reject
                });
            });
        }

        function testGeocode(address) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    type: "POST",
                    url: "TestAIAssistant.aspx/TestGeocode",
                    data: JSON.stringify({ address: address }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: resolve,
                    error: reject
                });
            });
        }

        function testNearbyShops(longitude, latitude) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    type: "POST",
                    url: "ChatAssistant.aspx/SearchNearbyShops",
                    data: JSON.stringify({ longitude: longitude, latitude: latitude, radius: 10 }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: resolve,
                    error: reject
                });
            });
        }

        function testRoute(origin, destination) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    type: "POST",
                    url: "ChatAssistant.aspx/GetRoute",
                    data: JSON.stringify({ origin: origin, destination: destination, strategy: 0 }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: resolve,
                    error: reject
                });
            });
        }

        // 页面加载完成后自动检查服务状态
        $(document).ready(function() {
            checkAIService();
            checkAmapService();
        });
    </script>
</body>
</html>
