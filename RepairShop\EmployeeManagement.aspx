<%@ Page Title="员工管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EmployeeManagement.aspx.cs" Inherits="RepairShop_EmployeeManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <style>
        .employee-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .employee-inactive {
            opacity: 0.6;
            background-color: #f5f5f5;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-users"></i> 员工管理</h2>
                <p class="text-muted">管理维修店员工信息</p>
            </div>
        </div>

        <!-- 消息提示 -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="row mb-3">
            <div class="col-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
            </div>
        </asp:Panel>

        <!-- 员工列表 -->
        <asp:Panel ID="pnlEmployeeList" runat="server">
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-list"></i> 员工列表</h5>
                                <asp:Button ID="btnAddEmployee" runat="server" Text="添加员工" CssClass="btn btn-light btn-sm" OnClick="btnAddEmployee_Click" />
                            </div>
                        </div>
                        <div class="card-body">
                            <asp:GridView ID="gvEmployees" runat="server" AutoGenerateColumns="False"
                                CssClass="table table-striped table-hover" DataKeyNames="EmployeeID"
                                OnRowCommand="gvEmployees_RowCommand" EmptyDataText="暂无员工记录">
                                <Columns>
                                    <asp:BoundField DataField="EmployeeName" HeaderText="姓名" />
                                    <asp:BoundField DataField="Position" HeaderText="职位" />
                                    <asp:BoundField DataField="Phone" HeaderText="电话" />
                                    <asp:BoundField DataField="Email" HeaderText="邮箱" />
                                    <asp:BoundField DataField="HireDate" HeaderText="入职日期" DataFormatString="{0:yyyy-MM-dd}" />
                                    <asp:TemplateField HeaderText="状态">
                                        <ItemTemplate>
                                            <span class='<%# Convert.ToBoolean(Eval("IsActive")) ? "status-badge status-active" : "status-badge status-inactive" %>'>
                                                <%# Convert.ToBoolean(Eval("IsActive")) ? "活跃" : "禁用" %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <div class="btn-group">
                                                <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-outline-primary"
                                                    CommandName="EditEmployee" CommandArgument='<%# Eval("EmployeeID") %>' ToolTip="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="lbtnToggleStatus" runat="server" 
                                                    CssClass='<%# Convert.ToBoolean(Eval("IsActive")) ? "btn btn-sm btn-outline-warning" : "btn btn-sm btn-outline-success" %>'
                                                    CommandName="ToggleStatus" CommandArgument='<%# Eval("EmployeeID") %>' 
                                                    ToolTip='<%# Convert.ToBoolean(Eval("IsActive")) ? "禁用" : "激活" %>'
                                                    OnClientClick="return confirm('确定要更改员工状态吗？');">
                                                    <i class='<%# Convert.ToBoolean(Eval("IsActive")) ? "fas fa-ban" : "fas fa-check" %>'></i>
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-outline-danger"
                                                    CommandName="DeleteEmployee" CommandArgument='<%# Eval("EmployeeID") %>' ToolTip="删除"
                                                    OnClientClick="return confirm('确定要删除这个员工吗？此操作不可撤销。');">
                                                    <i class="fas fa-trash"></i>
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <!-- 添加/编辑员工表单 -->
        <asp:Panel ID="pnlEmployeeForm" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus"></i> 
                                <asp:Label ID="lblFormTitle" runat="server" Text="添加员工"></asp:Label>
                            </h5>
                        </div>
                        <div class="card-body">
                            <asp:HiddenField ID="hfEmployeeID" runat="server" />
                            
                            <div class="form-group">
                                <label for="txtEmployeeName">员工姓名 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtEmployeeName" runat="server" CssClass="form-control" MaxLength="50"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmployeeName" runat="server" ControlToValidate="txtEmployeeName"
                                    ErrorMessage="请输入员工姓名" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmployeeForm"></asp:RequiredFieldValidator>
                            </div>

                            <div class="form-group">
                                <label for="txtPosition">职位 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtPosition" runat="server" CssClass="form-control" MaxLength="50" Text="技师"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvPosition" runat="server" ControlToValidate="txtPosition"
                                    ErrorMessage="请输入职位" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmployeeForm"></asp:RequiredFieldValidator>
                            </div>

                            <div class="form-group">
                                <label for="txtPhone">电话</label>
                                <asp:TextBox ID="txtPhone" runat="server" CssClass="form-control" MaxLength="20"></asp:TextBox>
                                <asp:RegularExpressionValidator ID="revPhone" runat="server" ControlToValidate="txtPhone"
                                    ErrorMessage="请输入有效的电话号码" Display="Dynamic" CssClass="text-danger" 
                                    ValidationExpression="^1[3-9]\d{9}$" ValidationGroup="EmployeeForm"></asp:RegularExpressionValidator>
                            </div>

                            <div class="form-group">
                                <label for="txtEmail">邮箱</label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" MaxLength="100"></asp:TextBox>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                    ErrorMessage="请输入有效的邮箱地址" Display="Dynamic" CssClass="text-danger" 
                                    ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" ValidationGroup="EmployeeForm"></asp:RegularExpressionValidator>
                            </div>

                            <div class="form-group text-center">
                                <asp:Button ID="btnSaveEmployee" runat="server" Text="保存" CssClass="btn btn-success mr-2" 
                                    OnClick="btnSaveEmployee_Click" ValidationGroup="EmployeeForm" />
                                <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-secondary" 
                                    OnClick="btnCancel_Click" CausesValidation="false" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>
</asp:Content>
