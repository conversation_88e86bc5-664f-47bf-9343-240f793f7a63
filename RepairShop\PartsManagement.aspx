<%@ Page Title="零件管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="RepairShop_PartsManagement" Codebehind="PartsManagement.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-cogs"></i> 零件管理</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 零件列表</h5>
                            <asp:Button ID="btnAddPart" runat="server" Text="添加零件" CssClass="btn btn-success btn-sm" OnClick="btnAddPart_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvParts" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" DataKeyNames="PartID"
                            OnRowCommand="gvParts_RowCommand" EmptyDataText="暂无零件记录" AllowPaging="true"
                            PageSize="15" OnPageIndexChanging="gvParts_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="PartID" HeaderText="ID" />
                                <asp:BoundField DataField="PartName" HeaderText="零件名称" />
                                <asp:BoundField DataField="PartNumber" HeaderText="零件编号" />
                                <asp:BoundField DataField="Price" HeaderText="价格(元)" DataFormatString="{0:N2}" />
                                <asp:BoundField DataField="StockQuantity" HeaderText="库存数量" />
                                <asp:BoundField DataField="Description" HeaderText="描述" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:Panel ID="pnlActions" runat="server" CssClass="btn-group">
                                            <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-warning"
                                                CommandName="EditPart" CommandArgument='<%# Eval("PartID") %>' ToolTip="编辑">
                                                <i class="fas fa-edit"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger"
                                                CommandName="DeletePart" CommandArgument='<%# Eval("PartID") %>' 
                                                ToolTip="删除" OnClientClick="return confirm('确定要删除这个零件吗？');">
                                                <i class="fas fa-trash"></i>
                                            </asp:LinkButton>
                                        </asp:Panel>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                            <PagerStyle HorizontalAlign="Center" CssClass="pagination-container" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加/编辑零件表单 -->
        <asp:Panel ID="pnlPartForm" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-plus"></i> 
                                <asp:Label ID="lblFormTitle" runat="server" Text="添加零件"></asp:Label>
                            </h5>
                        </div>
                        <div class="card-body">
                            <asp:HiddenField ID="hfPartID" runat="server" />
                            
                            <div class="form-group row">
                                <label for="txtPartName" class="col-sm-3 col-form-label">零件名称：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtPartName" runat="server" CssClass="form-control" MaxLength="100"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPartName" runat="server" ControlToValidate="txtPartName"
                                        ErrorMessage="零件名称不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="PartForm"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtPartNumber" class="col-sm-3 col-form-label">零件编号：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtPartNumber" runat="server" CssClass="form-control" MaxLength="50"></asp:TextBox>
                                    <small class="form-text text-muted">可选，用于内部管理</small>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtPrice" class="col-sm-3 col-form-label">价格(元)：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtPrice" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPrice" runat="server" ControlToValidate="txtPrice"
                                        ErrorMessage="价格不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="PartForm"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revPrice" runat="server" ControlToValidate="txtPrice"
                                        ErrorMessage="请输入有效的价格" Display="Dynamic" CssClass="text-danger" ValidationGroup="PartForm"
                                        ValidationExpression="^\d+(\.\d{1,2})?$"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtStockQuantity" class="col-sm-3 col-form-label">库存数量：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtStockQuantity" runat="server" CssClass="form-control" Text="0"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvStockQuantity" runat="server" ControlToValidate="txtStockQuantity"
                                        ErrorMessage="库存数量不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="PartForm"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revStockQuantity" runat="server" ControlToValidate="txtStockQuantity"
                                        ErrorMessage="请输入有效的数量" Display="Dynamic" CssClass="text-danger" ValidationGroup="PartForm"
                                        ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtDescription" class="col-sm-3 col-form-label">描述：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" MaxLength="500"></asp:TextBox>
                                    <small class="form-text text-muted">可选，零件的详细描述</small>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-9 offset-sm-3">
                                    <asp:Button ID="btnSavePart" runat="server" Text="保存" CssClass="btn btn-primary" ValidationGroup="PartForm" OnClick="btnSavePart_Click" />
                                    <asp:Button ID="btnCancelPart" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancelPart_Click" CausesValidation="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert" Visible="false"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content>