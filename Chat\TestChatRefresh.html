<!DOCTYPE html>
<html>
<head>
    <title>聊天刷新测试</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .input-test {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.typing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.idle {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .refresh-log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
        }
        .log-entry.blocked {
            color: #dc3545;
        }
        .log-entry.allowed {
            color: #28a745;
        }
        .improvement-list {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .improvement-list h4 {
            margin-top: 0;
            color: #007bff;
        }
        .improvement-list ul {
            margin-bottom: 0;
        }
        .improvement-list li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>聊天系统刷新优化测试</h1>
        
        <div class="improvement-list">
            <h4>🎉 优化改进内容</h4>
            <ul>
                <li><strong>Ajax异步刷新</strong>：使用Ajax替换__doPostBack，避免页面刷新打断用户输入</li>
                <li><strong>输入状态保护</strong>：检测用户输入状态，在输入时暂停自动刷新</li>
                <li><strong>智能更新策略</strong>：先检查是否有新消息，只在有新消息时才加载，减少不必要请求</li>
                <li><strong>更快的刷新频率</strong>：从10秒优化到5秒，提升实时性</li>
                <li><strong>状态保持</strong>：使用隐藏字段保持最后消息时间，避免重复加载</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>1. 输入状态检测测试</h3>
            <p>在下面的输入框中输入内容，观察状态变化：</p>
            <input type="text" class="input-test" id="testInput" placeholder="开始输入测试...">
            <div class="status idle" id="inputStatus">状态：空闲</div>
            <p><small>✅ 当显示"正在输入"时，自动刷新会被暂停，避免打断用户操作</small></p>
        </div>

        <div class="test-section">
            <h3>2. 刷新行为模拟</h3>
            <p>模拟聊天刷新行为，观察是否会在输入时被阻止：</p>
            <button onclick="startRefreshTest()">开始刷新测试</button>
            <button onclick="stopRefreshTest()">停止测试</button>
            <div class="refresh-log" id="refreshLog"></div>
        </div>

        <div class="test-section">
            <h3>3. 优化前后对比</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">方面</th>
                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">优化前</th>
                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">优化后</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;">刷新方式</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">__doPostBack (页面刷新)</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">Ajax (无刷新)</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;">用户体验</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">输入被打断</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">输入不受影响</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;">刷新频率</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">10秒</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">5秒</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;">网络效率</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">每次都刷新整个消息列表</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">只获取新消息</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;">智能检测</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">无</td>
                    <td style="border: 1px solid #ddd; padding: 10px;">检测输入状态，智能暂停</td>
                </tr>
            </table>
        </div>
    </div>

    <script>
        let isTyping = false;
        let refreshInterval = null;
        let typingTimeout = null;

        // 输入状态检测
        document.getElementById('testInput').addEventListener('input', function() {
            isTyping = true;
            updateStatus();
            
            clearTimeout(typingTimeout);
            typingTimeout = setTimeout(function() {
                isTyping = false;
                updateStatus();
            }, 2000);
        });

        document.getElementById('testInput').addEventListener('focus', function() {
            isTyping = true;
            updateStatus();
        });

        document.getElementById('testInput').addEventListener('blur', function() {
            setTimeout(function() {
                isTyping = false;
                updateStatus();
            }, 500);
        });

        function updateStatus() {
            const statusElement = document.getElementById('inputStatus');
            if (isTyping) {
                statusElement.textContent = '状态：正在输入';
                statusElement.className = 'status typing';
            } else {
                statusElement.textContent = '状态：空闲';
                statusElement.className = 'status idle';
            }
        }

        function addLogEntry(message, type) {
            const log = document.getElementById('refreshLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry ' + type;
            entry.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function simulateRefresh() {
            if (isTyping) {
                addLogEntry('刷新被阻止：用户正在输入', 'blocked');
            } else {
                addLogEntry('执行刷新：检查新消息', 'allowed');
            }
        }

        function startRefreshTest() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            addLogEntry('开始刷新测试 (每3秒检查一次)', 'allowed');
            refreshInterval = setInterval(simulateRefresh, 3000);
        }

        function stopRefreshTest() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                addLogEntry('刷新测试已停止', 'allowed');
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
