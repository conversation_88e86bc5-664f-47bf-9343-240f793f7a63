using System;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// 维修店管理类
/// </summary>
public static class ShopManager
{
    /// <summary>
    /// 根据用户ID获取维修店ID
    /// </summary>
    public static int GetShopIDByUserID(int userID)
    {
        string query = "SELECT ShopID FROM RepairShops WHERE UserID = @UserID";
        SqlParameter parameter = new SqlParameter("@UserID", userID);
        
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        
        return -1;
    }

    /// <summary>
    /// 获取维修店信息
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>维修店信息DataTable</returns>
    public static DataTable GetShopByID(int shopID)
    {
        string query = "SELECT * FROM RepairShops WHERE ShopID = @ShopID";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 更新维修店信息
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="shopName">店名</param>
    /// <param name="description">描述</param>
    /// <param name="address">地址</param>
    /// <param name="businessHours">营业时间</param>
    /// <param name="contactPerson">联系人</param>
    /// <param name="photoUrl">店铺照片URL</param>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateShopInfo(int shopID, string shopName, string description, string address, string businessHours, string contactPerson, string photoUrl, decimal? longitude = null, decimal? latitude = null)
    {
        try
        {
            // 检查Longitude和Latitude列是否存在
            bool columnsExist = CheckLocationColumnsExist();
            
            string query;
            SqlParameter[] parameters;
            
            if (columnsExist)
            {
                // 如果列存在，包含经纬度
                query = @"UPDATE RepairShops 
                        SET ShopName = @ShopName, 
                            Description = @Description, 
                            Address = @Address, 
                            BusinessHours = @BusinessHours, 
                            ContactPerson = @ContactPerson, 
                            PhotoUrl = @PhotoUrl,
                            Longitude = @Longitude,
                            Latitude = @Latitude
                        WHERE ShopID = @ShopID";

                parameters = new SqlParameter[]
                {
                    new SqlParameter("@ShopID", shopID),
                    new SqlParameter("@ShopName", shopName),
                    new SqlParameter("@Description", description ?? (object)DBNull.Value),
                    new SqlParameter("@Address", address),
                    new SqlParameter("@BusinessHours", businessHours ?? (object)DBNull.Value),
                    new SqlParameter("@ContactPerson", contactPerson ?? (object)DBNull.Value),
                    new SqlParameter("@PhotoUrl", photoUrl ?? (object)DBNull.Value),
                    new SqlParameter("@Longitude", longitude.HasValue ? (object)longitude.Value : DBNull.Value),
                    new SqlParameter("@Latitude", latitude.HasValue ? (object)latitude.Value : DBNull.Value)
                };
            }
            else
            {
                // 如果列不存在，不包含经纬度
                query = @"UPDATE RepairShops 
                        SET ShopName = @ShopName, 
                            Description = @Description, 
                            Address = @Address, 
                            BusinessHours = @BusinessHours, 
                            ContactPerson = @ContactPerson, 
                            PhotoUrl = @PhotoUrl
                        WHERE ShopID = @ShopID";

                parameters = new SqlParameter[]
                {
                    new SqlParameter("@ShopID", shopID),
                    new SqlParameter("@ShopName", shopName),
                    new SqlParameter("@Description", description ?? (object)DBNull.Value),
                    new SqlParameter("@Address", address),
                    new SqlParameter("@BusinessHours", businessHours ?? (object)DBNull.Value),
                    new SqlParameter("@ContactPerson", contactPerson ?? (object)DBNull.Value),
                    new SqlParameter("@PhotoUrl", photoUrl ?? (object)DBNull.Value)
                };
            }

            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新维修店信息时出错: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 检查RepairShops表中是否存在Longitude和Latitude列
    /// </summary>
    /// <returns>如果存在返回true，否则返回false</returns>
    private static bool CheckLocationColumnsExist()
    {
        try
        {
            string query = @"SELECT COUNT(*) 
                           FROM INFORMATION_SCHEMA.COLUMNS 
                           WHERE TABLE_NAME = 'RepairShops' 
                           AND COLUMN_NAME IN ('Longitude', 'Latitude')";
            
            object result = DatabaseHelper.ExecuteScalar(query);
            int count = Convert.ToInt32(result);
            
            // 如果找到了两列，返回true
            return count == 2;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"检查Longitude和Latitude列时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取服务类别
    /// </summary>
    public static DataTable GetServiceCategories()
    {
        string query = "SELECT CategoryID, CategoryName FROM ServiceCategories ORDER BY CategoryName";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 获取维修店服务
    /// </summary>
    public static DataTable GetShopServices(int shopID)
    {
        string query = @"SELECT rs.ServiceID, rs.CategoryID, sc.CategoryName, rs.ServiceName, 
                        rs.Description, rs.EstimatedTime, rs.BasePrice, rs.IsActive
                        FROM RepairServices rs
                        INNER JOIN ServiceCategories sc ON rs.CategoryID = sc.CategoryID
                        WHERE rs.ShopID = @ShopID
                        ORDER BY sc.CategoryName, rs.ServiceName";
        
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 更新服务
    /// </summary>
    public static bool UpdateService(int serviceID, int categoryID, string serviceName, string description, int estimatedTime, decimal basePrice, bool isActive)
    {
        string query = @"UPDATE RepairServices 
                        SET CategoryID = @CategoryID, 
                            ServiceName = @ServiceName, 
                            Description = @Description, 
                            EstimatedTime = @EstimatedTime, 
                            BasePrice = @BasePrice, 
                            IsActive = @IsActive 
                        WHERE ServiceID = @ServiceID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@ServiceID", serviceID),
            new SqlParameter("@CategoryID", categoryID),
            new SqlParameter("@ServiceName", serviceName),
            new SqlParameter("@Description", description ?? (object)DBNull.Value),
            new SqlParameter("@EstimatedTime", estimatedTime),
            new SqlParameter("@BasePrice", basePrice),
            new SqlParameter("@IsActive", isActive)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 添加服务
    /// </summary>
    public static int AddService(int shopID, int categoryID, string serviceName, string description, int estimatedTime, decimal basePrice)
    {
        string query = @"INSERT INTO RepairServices (ShopID, CategoryID, ServiceName, Description, EstimatedTime, BasePrice, IsActive) 
                        VALUES (@ShopID, @CategoryID, @ServiceName, @Description, @EstimatedTime, @BasePrice, 1);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@ShopID", shopID),
            new SqlParameter("@CategoryID", categoryID),
            new SqlParameter("@ServiceName", serviceName),
            new SqlParameter("@Description", description ?? (object)DBNull.Value),
            new SqlParameter("@EstimatedTime", estimatedTime),
            new SqlParameter("@BasePrice", basePrice)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 获取维修店预约列表
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>预约列表DataTable</returns>
    public static DataTable GetShopAppointments(int shopID)
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID
                        ORDER BY a.AppointmentDate DESC";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取维修店未处理的预约
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>未处理预约列表DataTable</returns>
    public static DataTable GetPendingAppointments(int shopID)
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID AND a.Status = 'Pending'
                        ORDER BY a.AppointmentDate";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 确认预约
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool ConfirmAppointment(int appointmentID)
    {
        string query = "UPDATE Appointments SET Status = 'Confirmed' WHERE AppointmentID = @AppointmentID";
        SqlParameter parameter = new SqlParameter("@AppointmentID", appointmentID);
        int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
        return result > 0;
    }

    /// <summary>
    /// 完成预约并添加维修记录
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <param name="diagnosisDetails">诊断详情</param>
    /// <param name="partsReplaced">更换零件</param>
    /// <param name="laborCost">工时费</param>
    /// <param name="partsCost">零件费</param>
    /// <param name="technicianName">技师姓名</param>
    /// <returns>成功返回记录ID，失败返回-1</returns>
    public static int CompleteAppointment(int appointmentID, string diagnosisDetails, string partsReplaced, decimal laborCost, decimal partsCost, string technicianName)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"CompleteAppointment方法开始 - 预约ID: {appointmentID}");

            // 验证参数
            if (appointmentID <= 0)
            {
                System.Diagnostics.Debug.WriteLine("预约ID无效");
                return -1;
            }

            if (string.IsNullOrEmpty(diagnosisDetails))
            {
                System.Diagnostics.Debug.WriteLine("诊断详情为空");
                return -1;
            }

            if (string.IsNullOrEmpty(technicianName))
            {
                System.Diagnostics.Debug.WriteLine("技师姓名为空");
                return -1;
            }

            // 检查预约是否存在且状态为已确认
            string checkQuery = "SELECT Status FROM Appointments WHERE AppointmentID = @AppointmentID";
            SqlParameter checkParam = new SqlParameter("@AppointmentID", appointmentID);
            object statusObj = DatabaseHelper.ExecuteScalar(checkQuery, checkParam);

            if (statusObj == null || statusObj == DBNull.Value)
            {
                System.Diagnostics.Debug.WriteLine("预约不存在");
                return -1;
            }

            string status = statusObj.ToString();
            if (status != "Confirmed")
            {
                System.Diagnostics.Debug.WriteLine($"预约状态不是已确认，当前状态: {status}");
                return -1;
            }

            // 开始更新预约状态
            string updateQuery = "UPDATE Appointments SET Status = 'Completed' WHERE AppointmentID = @AppointmentID";
            SqlParameter updateParam = new SqlParameter("@AppointmentID", appointmentID);
            int updateResult = DatabaseHelper.ExecuteNonQuery(updateQuery, updateParam);
            
            System.Diagnostics.Debug.WriteLine($"更新预约状态结果: {updateResult}");
            
            if (updateResult <= 0)
            {
                System.Diagnostics.Debug.WriteLine("更新预约状态失败");
                return -1;
            }

            // 计算总费用
            decimal totalCost = laborCost + partsCost;

            // 添加维修记录
            string insertQuery = @"INSERT INTO ServiceRecords (AppointmentID, CompletedDate, DiagnosisDetails, PartsReplaced, LaborCost, PartsCost, TotalCost, TechnicianName) 
                                VALUES (@AppointmentID, GETDATE(), @DiagnosisDetails, @PartsReplaced, @LaborCost, @PartsCost, @TotalCost, @TechnicianName);
                                SELECT SCOPE_IDENTITY()";

            SqlParameter[] insertParams =
            {
                new SqlParameter("@AppointmentID", appointmentID),
                new SqlParameter("@DiagnosisDetails", diagnosisDetails),
                new SqlParameter("@PartsReplaced", !string.IsNullOrEmpty(partsReplaced) ? partsReplaced : (object)DBNull.Value),
                new SqlParameter("@LaborCost", laborCost),
                new SqlParameter("@PartsCost", partsCost),
                new SqlParameter("@TotalCost", totalCost),
                new SqlParameter("@TechnicianName", technicianName)
            };

            object result = DatabaseHelper.ExecuteScalar(insertQuery, insertParams);
            System.Diagnostics.Debug.WriteLine($"插入维修记录结果: {result}");
            
            if (result != null && result != DBNull.Value)
            {
                int recordID = Convert.ToInt32(result);
                System.Diagnostics.Debug.WriteLine($"成功添加维修记录，ID: {recordID}");
                return recordID;
            }
            
            System.Diagnostics.Debug.WriteLine("添加维修记录失败");
            return -1;
        }
        catch (Exception ex)
        {
            // 记录错误
            System.Diagnostics.Debug.WriteLine($"CompleteAppointment方法出错: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            return -1;
        }
    }

    /// <summary>
    /// 获取维修店的维修记录
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>维修记录DataTable</returns>
    public static DataTable GetShopServiceRecords(int shopID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.AppointmentID,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID
                        ORDER BY sr.CompletedDate DESC";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取维修店的评价
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>评价DataTable</returns>
    public static DataTable GetShopReviews(int shopID)
    {
        string query = @"SELECT r.ReviewID, r.Rating, r.Comments, r.ReviewDate,
                        co.FullName AS OwnerName, sr.RecordID,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        s.ServiceName
                        FROM Reviews r
                        INNER JOIN ServiceRecords sr ON r.ServiceRecordID = sr.RecordID
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON r.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE r.ShopID = @ShopID
                        ORDER BY r.ReviewDate DESC";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取维修店的评价（带分页）
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页记录数</param>
    /// <returns>评价DataTable</returns>
    public static DataTable GetShopReviews(int shopID, int pageNumber, int pageSize)
    {
        int offset = (pageNumber - 1) * pageSize;
        
        string query = @"SELECT r.ReviewID, r.Rating, r.Comments, r.ReviewDate,
                        u.Username, sr.RecordID,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        s.ServiceName
                        FROM Reviews r
                        INNER JOIN ServiceRecords sr ON r.ServiceRecordID = sr.RecordID
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON r.OwnerID = co.OwnerID
                        INNER JOIN Users u ON co.UserID = u.UserID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE r.ShopID = @ShopID
                        ORDER BY r.ReviewDate DESC
                        OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
                        
        SqlParameter[] parameters = {
            new SqlParameter("@ShopID", shopID),
            new SqlParameter("@Offset", offset),
            new SqlParameter("@PageSize", pageSize)
        };
        
        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 获取维修店的评价总数
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>评价总数</returns>
    public static int GetShopReviewCount(int shopID)
    {
        string query = "SELECT COUNT(*) FROM Reviews WHERE ShopID = @ShopID";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        
        return 0;
    }
    
    /// <summary>
    /// 删除评价
    /// </summary>
    /// <param name="reviewID">评价ID</param>
    /// <param name="ownerID">车主ID（如果是管理员删除，可以传入null）</param>
    /// <param name="isAdmin">是否是管理员操作</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteReview(int reviewID, int? ownerID = null, bool isAdmin = false)
    {
        try
        {
            string query;
            SqlParameter[] parameters;
            
            if (isAdmin)
            {
                // 管理员可以删除任何评论
                query = "DELETE FROM Reviews WHERE ReviewID = @ReviewID";
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@ReviewID", reviewID)
                };
            }
            else
            {
                // 车主只能删除自己的评论
                query = "DELETE FROM Reviews WHERE ReviewID = @ReviewID AND OwnerID = @OwnerID";
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@ReviewID", reviewID),
                    new SqlParameter("@OwnerID", ownerID)
                };
            }
            
            // 删除评论前先删除相关的照片
            string deletePhotosQuery = "DELETE FROM ReviewPhotos WHERE ReviewID = @ReviewID";
            SqlParameter photoParam = new SqlParameter("@ReviewID", reviewID);
            DatabaseHelper.ExecuteNonQuery(deletePhotosQuery, photoParam);
            
            // 删除评论
            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"DeleteReview方法出错: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 获取维修店详细信息
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>维修店详细信息DataTable</returns>
    public static DataTable GetShopDetails(int shopID)
    {
        string query = @"SELECT s.ShopID, s.ShopName, s.Address, s.Description, s.BusinessHours, 
                        s.ContactPerson, s.PhotoUrl AS LogoUrl, 
                        ISNULL((SELECT AVG(CAST(Rating AS FLOAT)) FROM Reviews WHERE ShopID = s.ShopID), 0) AS Rating,
                        ISNULL((SELECT COUNT(*) FROM Reviews WHERE ShopID = s.ShopID), 0) AS ReviewCount,
                        '***********' AS Phone,
                        '' AS Qualifications
                        FROM RepairShops s
                        WHERE s.ShopID = @ShopID";
                        
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取维修店统计信息
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>统计信息DataTable</returns>
    public static DataTable GetShopStatistics(int shopID)
    {
        string query = @"SELECT 
                        (SELECT COUNT(1) FROM Appointments WHERE ShopID = @ShopID AND Status = 'Pending') AS PendingAppointments,
                        (SELECT COUNT(1) FROM Appointments WHERE ShopID = @ShopID AND Status = 'Confirmed') AS ConfirmedAppointments,
                        (SELECT COUNT(1) FROM Appointments WHERE ShopID = @ShopID AND Status = 'Completed') AS CompletedAppointments,
                        (SELECT COUNT(1) FROM RepairServices WHERE ShopID = @ShopID) AS TotalServices,
                        (SELECT AVG(CAST(Rating AS DECIMAL(3,2))) FROM Reviews WHERE ShopID = @ShopID) AS AverageRating,
                        (SELECT COUNT(1) FROM Reviews WHERE ShopID = @ShopID) AS TotalReviews,
                        (SELECT SUM(TotalCost) FROM ServiceRecords sr
                         INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                         WHERE a.ShopID = @ShopID) AS TotalRevenue";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 删除服务
    /// </summary>
    /// <param name="serviceID">服务ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteService(int serviceID)
    {
        string query = "DELETE FROM RepairServices WHERE ServiceID = @ServiceID";
        SqlParameter parameter = new SqlParameter("@ServiceID", serviceID);
        
        int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
        return result > 0;
    }

    /// <summary>
    /// 创建默认维修店信息
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="shopName">店铺名称</param>
    /// <returns>成功返回新创建的ShopID，失败返回-1</returns>
    public static int CreateDefaultShop(int userID, string shopName)
    {
        string query = @"INSERT INTO RepairShops (UserID, ShopName, Address, Rating) 
                        VALUES (@UserID, @ShopName, '请更新地址', 0);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@ShopName", shopName)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 获取所有维修店（前台用）
    /// </summary>
    public static DataTable GetAllShopsForFrontend(int filter = 0)
    {
        string orderBy = "ShopName";
        if (filter == 1) orderBy = "Rating DESC, ShopName";
        string query = $"SELECT ShopID, ShopName, Address, Rating, ContactPerson, ISNULL(PhotoUrl, '/Images/default-shop.png') AS LogoUrl, '***********' AS Phone, 0 AS ReviewCount FROM RepairShops ORDER BY {orderBy}";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 按关键词搜索维修店（前台用）
    /// </summary>
    public static DataTable SearchShopsForFrontend(string searchQuery, int filter = 0)
    {
        string orderBy = "ShopName";
        if (filter == 1) orderBy = "Rating DESC, ShopName";
        string query = $"SELECT ShopID, ShopName, Address, Rating, ContactPerson, ISNULL(PhotoUrl, '/Images/default-shop.png') AS LogoUrl, '***********' AS Phone, 0 AS ReviewCount FROM RepairShops WHERE ShopName LIKE @kw OR Address LIKE @kw ORDER BY {orderBy}";
        SqlParameter param = new SqlParameter("@kw", "%" + searchQuery + "%");
        return DatabaseHelper.ExecuteQuery(query, param);
    }

    /// <summary>
    /// 获取所有维修记录（管理员使用）
    /// </summary>
    /// <returns>包含所有维修记录的DataTable</returns>
    public static DataTable GetAllServiceRecords()
    {
        string query = @"SELECT sr.RecordID, sr.AppointmentID, sr.CompletedDate, sr.DiagnosisDetails, 
                        sr.PartsReplaced, sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.Status,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, rs.ShopName, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        ORDER BY sr.CompletedDate DESC";
        return DatabaseHelper.ExecuteQuery(query);
    }
    
    /// <summary>
    /// 获取所有维修记录（管理员使用，带分页）
    /// </summary>
    /// <param name="pageIndex">页码（从0开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <returns>指定页的维修记录</returns>
    public static DataTable GetAllServiceRecordsPaged(int pageIndex, int pageSize)
    {
        int offset = pageIndex * pageSize;
        
        string query = @"SELECT sr.RecordID, sr.AppointmentID, sr.CompletedDate, sr.DiagnosisDetails, 
                        sr.PartsReplaced, sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.Status,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.OwnerID, co.FullName AS OwnerName, rs.ShopID, rs.ShopName, s.ServiceID, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        ORDER BY sr.CompletedDate DESC
                        OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
        
        SqlParameter[] parameters = {
            new SqlParameter("@Offset", offset),
            new SqlParameter("@PageSize", pageSize)
        };
        
        return DatabaseHelper.ExecuteQuery(query, parameters);
    }
    
    /// <summary>
    /// 获取维修记录总数
    /// </summary>
    /// <returns>维修记录总数</returns>
    public static int GetServiceRecordsCount()
    {
        string query = "SELECT COUNT(*) FROM ServiceRecords";
        object result = DatabaseHelper.ExecuteScalar(query);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return 0;
    }
    
    /// <summary>
    /// 根据ID获取维修记录详情
    /// </summary>
    /// <param name="recordID">维修记录ID</param>
    /// <returns>维修记录详情</returns>
    public static DataTable GetServiceRecordByID(int recordID)
    {
        string query = @"SELECT sr.RecordID, sr.AppointmentID, sr.CompletedDate, sr.DiagnosisDetails, 
                        sr.PartsReplaced, sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.Status, a.Description,
                        c.CarID, c.Make, c.Model, c.LicensePlate, c.Year, c.VIN,
                        co.OwnerID, co.FullName AS OwnerName, co.Phone AS OwnerPhone, co.Email AS OwnerEmail,
                        rs.ShopID, rs.ShopName, rs.Address AS ShopAddress,
                        s.ServiceID, s.ServiceName, s.Description AS ServiceDescription, s.BasePrice
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE sr.RecordID = @RecordID";
        
        SqlParameter parameter = new SqlParameter("@RecordID", recordID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }
    
    /// <summary>
    /// 按条件搜索维修记录
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="ownerName">车主姓名（模糊搜索）</param>
    /// <param name="shopName">维修店名称（模糊搜索）</param>
    /// <param name="minCost">最小费用</param>
    /// <param name="maxCost">最大费用</param>
    /// <returns>符合条件的维修记录</returns>
    public static DataTable SearchServiceRecords(DateTime? startDate = null, DateTime? endDate = null, string ownerName = null, string shopName = null, decimal? minCost = null, decimal? maxCost = null)
    {
        System.Collections.Generic.List<SqlParameter> parameters = new System.Collections.Generic.List<SqlParameter>();
        
        string query = @"SELECT sr.RecordID, sr.AppointmentID, sr.CompletedDate, sr.DiagnosisDetails, 
                        sr.PartsReplaced, sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.Status,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, rs.ShopName, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE 1=1";
        
        if (startDate.HasValue)
        {
            query += " AND sr.CompletedDate >= @StartDate";
            parameters.Add(new SqlParameter("@StartDate", startDate.Value));
        }
        
        if (endDate.HasValue)
        {
            query += " AND sr.CompletedDate <= @EndDate";
            parameters.Add(new SqlParameter("@EndDate", endDate.Value));
        }
        
        if (!string.IsNullOrEmpty(ownerName))
        {
            query += " AND co.FullName LIKE @OwnerName";
            parameters.Add(new SqlParameter("@OwnerName", $"%{ownerName}%"));
        }
        
        if (!string.IsNullOrEmpty(shopName))
        {
            query += " AND rs.ShopName LIKE @ShopName";
            parameters.Add(new SqlParameter("@ShopName", $"%{shopName}%"));
        }
        
        if (minCost.HasValue)
        {
            query += " AND sr.TotalCost >= @MinCost";
            parameters.Add(new SqlParameter("@MinCost", minCost.Value));
        }
        
        if (maxCost.HasValue)
        {
            query += " AND sr.TotalCost <= @MaxCost";
            parameters.Add(new SqlParameter("@MaxCost", maxCost.Value));
        }
        
        query += " ORDER BY sr.CompletedDate DESC";
        
        return DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
    }
    
    /// <summary>
    /// 更新维修记录（管理员使用）
    /// </summary>
    /// <param name="recordID">记录ID</param>
    /// <param name="completedDate">完成日期</param>
    /// <param name="diagnosisDetails">诊断详情</param>
    /// <param name="partsReplaced">更换部件</param>
    /// <param name="laborCost">人工费用</param>
    /// <param name="partsCost">零件费用</param>
    /// <param name="technicianName">技师姓名</param>
    /// <returns>更新是否成功</returns>
    public static bool UpdateServiceRecord(int recordID, DateTime completedDate, string diagnosisDetails, string partsReplaced, decimal laborCost, decimal partsCost, string technicianName)
    {
        try
        {
            decimal totalCost = laborCost + partsCost;
            
            string query = @"UPDATE ServiceRecords SET 
                            CompletedDate = @CompletedDate,
                            DiagnosisDetails = @DiagnosisDetails,
                            PartsReplaced = @PartsReplaced,
                            LaborCost = @LaborCost,
                            PartsCost = @PartsCost,
                            TotalCost = @TotalCost,
                            TechnicianName = @TechnicianName
                            WHERE RecordID = @RecordID";
            
            SqlParameter[] parameters = {
                new SqlParameter("@RecordID", recordID),
                new SqlParameter("@CompletedDate", completedDate),
                new SqlParameter("@DiagnosisDetails", diagnosisDetails ?? (object)DBNull.Value),
                new SqlParameter("@PartsReplaced", partsReplaced ?? (object)DBNull.Value),
                new SqlParameter("@LaborCost", laborCost),
                new SqlParameter("@PartsCost", partsCost),
                new SqlParameter("@TotalCost", totalCost),
                new SqlParameter("@TechnicianName", technicianName)
            };
            
            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新维修记录时出错: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 删除维修记录（管理员使用）
    /// </summary>
    /// <param name="recordID">记录ID</param>
    /// <returns>删除是否成功</returns>
    public static bool DeleteServiceRecord(int recordID)
    {
        try
        {
            // 首先检查是否有关联的评价
            string checkReviewQuery = "SELECT COUNT(*) FROM Reviews WHERE ServiceRecordID = @RecordID";
            SqlParameter checkParam = new SqlParameter("@RecordID", recordID);
            int reviewCount = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkReviewQuery, checkParam));
            
            if (reviewCount > 0)
            {
                // 先删除关联的评价
                string deleteReviewsQuery = "DELETE FROM Reviews WHERE ServiceRecordID = @RecordID";
                DatabaseHelper.ExecuteNonQuery(deleteReviewsQuery, checkParam);
                
                // 删除评价照片
                string deleteReviewPhotosQuery = @"DELETE FROM ReviewPhotos 
                                               WHERE ReviewID IN (SELECT ReviewID FROM Reviews WHERE ServiceRecordID = @RecordID)";
                DatabaseHelper.ExecuteNonQuery(deleteReviewPhotosQuery, checkParam);
            }
            
            // 删除维修记录
            string query = "DELETE FROM ServiceRecords WHERE RecordID = @RecordID";
            SqlParameter parameter = new SqlParameter("@RecordID", recordID);
            
            int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除维修记录时出错: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 手动创建维修记录（管理员使用）
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <param name="completedDate">完成日期</param>
    /// <param name="diagnosisDetails">诊断详情</param>
    /// <param name="partsReplaced">更换部件</param>
    /// <param name="laborCost">人工费用</param>
    /// <param name="partsCost">零件费用</param>
    /// <param name="technicianName">技师姓名</param>
    /// <returns>新记录ID，失败返回-1</returns>
    public static int CreateServiceRecord(int appointmentID, DateTime completedDate, string diagnosisDetails, string partsReplaced, decimal laborCost, decimal partsCost, string technicianName)
    {
        try
        {
            // 检查预约是否存在
            string checkQuery = "SELECT Status FROM Appointments WHERE AppointmentID = @AppointmentID";
            SqlParameter checkParam = new SqlParameter("@AppointmentID", appointmentID);
            object statusObj = DatabaseHelper.ExecuteScalar(checkQuery, checkParam);
            
            if (statusObj == null || statusObj == DBNull.Value)
            {
                System.Diagnostics.Debug.WriteLine($"预约ID {appointmentID} 不存在");
                return -1;
            }
            
            // 检查该预约是否已有维修记录
            string checkRecordQuery = "SELECT COUNT(*) FROM ServiceRecords WHERE AppointmentID = @AppointmentID";
            int recordExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkRecordQuery, checkParam));
            
            if (recordExists > 0)
            {
                System.Diagnostics.Debug.WriteLine($"预约ID {appointmentID} 已有维修记录");
                return -1;
            }
            
            // 计算总费用
            decimal totalCost = laborCost + partsCost;
            
            // 创建维修记录
            string query = @"INSERT INTO ServiceRecords (
                            AppointmentID, CompletedDate, DiagnosisDetails, 
                            PartsReplaced, LaborCost, PartsCost, TotalCost, TechnicianName)
                            VALUES (
                            @AppointmentID, @CompletedDate, @DiagnosisDetails,
                            @PartsReplaced, @LaborCost, @PartsCost, @TotalCost, @TechnicianName);
                            SELECT SCOPE_IDENTITY()";
            
            SqlParameter[] parameters = {
                new SqlParameter("@AppointmentID", appointmentID),
                new SqlParameter("@CompletedDate", completedDate),
                new SqlParameter("@DiagnosisDetails", diagnosisDetails ?? (object)DBNull.Value),
                new SqlParameter("@PartsReplaced", partsReplaced ?? (object)DBNull.Value),
                new SqlParameter("@LaborCost", laborCost),
                new SqlParameter("@PartsCost", partsCost),
                new SqlParameter("@TotalCost", totalCost),
                new SqlParameter("@TechnicianName", technicianName)
            };
            
            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            
            if (result != null && result != DBNull.Value)
            {
                int recordID = Convert.ToInt32(result);
                
                // 更新预约状态为已完成
                string updateQuery = "UPDATE Appointments SET Status = 'Completed' WHERE AppointmentID = @AppointmentID";
                DatabaseHelper.ExecuteNonQuery(updateQuery, checkParam);
                
                return recordID;
            }
            
            return -1;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"创建维修记录时出错: {ex.Message}");
            return -1;
        }
    }

    #region 员工管理

    /// <summary>
    /// 获取维修店员工列表
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="includeInactive">是否包含非活跃员工</param>
    /// <returns>员工列表DataTable</returns>
    public static DataTable GetEmployees(int shopID, bool includeInactive = true)
    {
        string query = @"SELECT EmployeeID, EmployeeName, Position, Phone, Email, HireDate, IsActive, CreatedDate, UpdatedDate
                        FROM Employees
                        WHERE ShopID = @ShopID";

        if (!includeInactive)
        {
            query += " AND IsActive = 1";
        }

        query += " ORDER BY IsActive DESC, EmployeeName";

        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取维修店活跃员工列表（用于下拉列表）
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>活跃员工列表DataTable</returns>
    public static DataTable GetActiveEmployees(int shopID)
    {
        string query = @"SELECT EmployeeID, EmployeeName, Position
                        FROM Employees
                        WHERE ShopID = @ShopID AND IsActive = 1
                        ORDER BY EmployeeName";

        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 添加员工
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <param name="employeeName">员工姓名</param>
    /// <param name="position">职位</param>
    /// <param name="phone">电话</param>
    /// <param name="email">邮箱</param>
    /// <returns>成功返回员工ID，失败返回-1</returns>
    public static int AddEmployee(int shopID, string employeeName, string position, string phone, string email)
    {
        string query = @"INSERT INTO Employees (ShopID, EmployeeName, Position, Phone, Email, HireDate, IsActive, CreatedDate, UpdatedDate)
                        VALUES (@ShopID, @EmployeeName, @Position, @Phone, @Email, GETDATE(), 1, GETDATE(), GETDATE());
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@ShopID", shopID),
            new SqlParameter("@EmployeeName", employeeName),
            new SqlParameter("@Position", position),
            new SqlParameter("@Phone", phone ?? (object)DBNull.Value),
            new SqlParameter("@Email", email ?? (object)DBNull.Value)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 更新员工信息
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    /// <param name="employeeName">员工姓名</param>
    /// <param name="position">职位</param>
    /// <param name="phone">电话</param>
    /// <param name="email">邮箱</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateEmployee(int employeeID, string employeeName, string position, string phone, string email)
    {
        string query = @"UPDATE Employees
                        SET EmployeeName = @EmployeeName, Position = @Position, Phone = @Phone, Email = @Email, UpdatedDate = GETDATE()
                        WHERE EmployeeID = @EmployeeID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@EmployeeID", employeeID),
            new SqlParameter("@EmployeeName", employeeName),
            new SqlParameter("@Position", position),
            new SqlParameter("@Phone", phone ?? (object)DBNull.Value),
            new SqlParameter("@Email", email ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 切换员工状态（激活/禁用）
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    /// <param name="isActive">是否激活</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool ToggleEmployeeStatus(int employeeID, bool isActive)
    {
        string query = @"UPDATE Employees
                        SET IsActive = @IsActive, UpdatedDate = GETDATE()
                        WHERE EmployeeID = @EmployeeID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@EmployeeID", employeeID),
            new SqlParameter("@IsActive", isActive)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 删除员工
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteEmployee(int employeeID)
    {
        string query = "DELETE FROM Employees WHERE EmployeeID = @EmployeeID";
        SqlParameter parameter = new SqlParameter("@EmployeeID", employeeID);

        int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
        return result > 0;
    }

    /// <summary>
    /// 获取员工详细信息
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    /// <returns>员工详细信息DataTable</returns>
    public static DataTable GetEmployeeDetails(int employeeID)
    {
        string query = @"SELECT EmployeeID, ShopID, EmployeeName, Position, Phone, Email, HireDate, IsActive, CreatedDate, UpdatedDate
                        FROM Employees
                        WHERE EmployeeID = @EmployeeID";

        SqlParameter parameter = new SqlParameter("@EmployeeID", employeeID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    #endregion
}