using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_EmployeeManagement : System.Web.UI.Page
{
    private int userID;
    private int shopID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户登录和权限
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        shopID = ShopManager.GetShopIDByUserID(userID);

        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            LoadEmployees();
        }
    }

    #region 员工列表管理

    /// <summary>
    /// 加载员工列表
    /// </summary>
    private void LoadEmployees()
    {
        try
        {
            DataTable employeesTable = ShopManager.GetEmployees(shopID, true);
            gvEmployees.DataSource = employeesTable;
            gvEmployees.DataBind();
        }
        catch (Exception ex)
        {
            ShowMessage("加载员工列表时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// GridView行命令事件处理
    /// </summary>
    protected void gvEmployees_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            int employeeID = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "EditEmployee":
                    EditEmployee(employeeID);
                    break;
                case "ToggleStatus":
                    ToggleEmployeeStatus(employeeID);
                    break;
                case "DeleteEmployee":
                    DeleteEmployee(employeeID);
                    break;
            }
        }
        catch (Exception ex)
        {
            ShowMessage("操作失败：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 添加员工按钮点击事件
    /// </summary>
    protected void btnAddEmployee_Click(object sender, EventArgs e)
    {
        ShowEmployeeForm(true);
    }

    #endregion

    #region 员工表单管理

    /// <summary>
    /// 显示员工表单
    /// </summary>
    /// <param name="isAdd">是否为添加模式</param>
    private void ShowEmployeeForm(bool isAdd)
    {
        pnlEmployeeList.Visible = false;
        pnlEmployeeForm.Visible = true;
        pnlMessage.Visible = false;

        if (isAdd)
        {
            lblFormTitle.Text = "添加员工";
            hfEmployeeID.Value = "";
            ClearForm();
        }
        else
        {
            lblFormTitle.Text = "编辑员工";
        }
    }

    /// <summary>
    /// 编辑员工
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    private void EditEmployee(int employeeID)
    {
        try
        {
            DataTable employeeTable = ShopManager.GetEmployeeDetails(employeeID);
            if (employeeTable.Rows.Count > 0)
            {
                DataRow row = employeeTable.Rows[0];
                
                hfEmployeeID.Value = employeeID.ToString();
                txtEmployeeName.Text = row["EmployeeName"].ToString();
                txtPosition.Text = row["Position"].ToString();
                txtPhone.Text = row["Phone"].ToString();
                txtEmail.Text = row["Email"].ToString();

                ShowEmployeeForm(false);
            }
            else
            {
                ShowMessage("找不到指定的员工信息。", "warning");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("加载员工信息时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 切换员工状态
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    private void ToggleEmployeeStatus(int employeeID)
    {
        try
        {
            // 获取当前状态
            DataTable employeeTable = ShopManager.GetEmployeeDetails(employeeID);
            if (employeeTable.Rows.Count > 0)
            {
                bool currentStatus = Convert.ToBoolean(employeeTable.Rows[0]["IsActive"]);
                bool newStatus = !currentStatus;

                bool success = ShopManager.ToggleEmployeeStatus(employeeID, newStatus);
                if (success)
                {
                    string statusText = newStatus ? "激活" : "禁用";
                    ShowMessage($"员工状态已成功{statusText}。", "success");
                    LoadEmployees();
                }
                else
                {
                    ShowMessage("更新员工状态失败。", "danger");
                }
            }
        }
        catch (Exception ex)
        {
            ShowMessage("更新员工状态时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 删除员工
    /// </summary>
    /// <param name="employeeID">员工ID</param>
    private void DeleteEmployee(int employeeID)
    {
        try
        {
            bool success = ShopManager.DeleteEmployee(employeeID);
            if (success)
            {
                ShowMessage("员工已成功删除。", "success");
                LoadEmployees();
            }
            else
            {
                ShowMessage("删除员工失败。", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("删除员工时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 保存员工按钮点击事件
    /// </summary>
    protected void btnSaveEmployee_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
            return;

        try
        {
            string employeeName = txtEmployeeName.Text.Trim();
            string position = txtPosition.Text.Trim();
            string phone = txtPhone.Text.Trim();
            string email = txtEmail.Text.Trim();

            if (string.IsNullOrEmpty(phone))
                phone = null;
            if (string.IsNullOrEmpty(email))
                email = null;

            bool success = false;
            string message = "";

            if (string.IsNullOrEmpty(hfEmployeeID.Value))
            {
                // 添加员工
                int employeeID = ShopManager.AddEmployee(shopID, employeeName, position, phone, email);
                success = employeeID > 0;
                message = success ? "员工添加成功。" : "添加员工失败。";
            }
            else
            {
                // 更新员工
                int employeeID = Convert.ToInt32(hfEmployeeID.Value);
                success = ShopManager.UpdateEmployee(employeeID, employeeName, position, phone, email);
                message = success ? "员工信息更新成功。" : "更新员工信息失败。";
            }

            if (success)
            {
                ShowMessage(message, "success");
                ShowEmployeeList();
                LoadEmployees();
            }
            else
            {
                ShowMessage(message, "danger");
            }
        }
        catch (Exception ex)
        {
            ShowMessage("保存员工信息时出错：" + ex.Message, "danger");
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        ShowEmployeeList();
    }

    /// <summary>
    /// 显示员工列表
    /// </summary>
    private void ShowEmployeeList()
    {
        pnlEmployeeForm.Visible = false;
        pnlEmployeeList.Visible = true;
        ClearForm();
    }

    /// <summary>
    /// 清空表单
    /// </summary>
    private void ClearForm()
    {
        hfEmployeeID.Value = "";
        txtEmployeeName.Text = "";
        txtPosition.Text = "技师";
        txtPhone.Text = "";
        txtEmail.Text = "";
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 显示消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="type">消息类型</param>
    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = "alert alert-" + type;
        pnlMessage.Visible = true;
    }

    #endregion
}
