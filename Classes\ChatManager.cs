using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;

/// <summary>
/// 聊天系统管理类
/// </summary>
public static class ChatManager
{
    #region 聊天室管理

    /// <summary>
    /// 获取用户可访问的聊天室列表
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <returns>聊天室列表</returns>
    public static DataTable GetUserChatRooms(int userID)
    {
        string query = @"
            SELECT DISTINCT cr.RoomID, cr.RoomName, cr.RoomType, cr.Description, 
                   cr.LastMessageDate, cr.CreatedDate,
                   CASE WHEN crm.UserID IS NOT NULL THEN 1 ELSE 0 END AS IsMember,
                   (SELECT COUNT(*) FROM ChatMessages cm WHERE cm.RoomID = cr.RoomID AND cm.IsDeleted = 0) AS MessageCount,
                   (SELECT COUNT(*) FROM ChatMessages cm 
                    WHERE cm.RoomID = cr.RoomID AND cm.IsDeleted = 0 
                    AND cm.SentDate > ISNULL(crm.LastReadDate, '1900-01-01')) AS UnreadCount
            FROM ChatRooms cr
            LEFT JOIN ChatRoomMembers crm ON cr.RoomID = crm.RoomID AND crm.UserID = @UserID AND crm.IsActive = 1
            WHERE cr.IsActive = 1 
            AND (cr.RoomType = 'Public' OR crm.UserID IS NOT NULL)
            ORDER BY cr.RoomType DESC, cr.LastMessageDate DESC, cr.CreatedDate DESC";

        SqlParameter parameter = new SqlParameter("@UserID", userID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 创建私聊房间
    /// </summary>
    /// <param name="user1ID">用户1 ID</param>
    /// <param name="user2ID">用户2 ID</param>
    /// <returns>聊天室ID，失败返回-1</returns>
    public static int CreatePrivateChatRoom(int user1ID, int user2ID)
    {
        // 检查是否已存在私聊房间
        string checkQuery = @"
            SELECT cr.RoomID 
            FROM ChatRooms cr
            INNER JOIN ChatRoomMembers crm1 ON cr.RoomID = crm1.RoomID AND crm1.UserID = @User1ID
            INNER JOIN ChatRoomMembers crm2 ON cr.RoomID = crm2.RoomID AND crm2.UserID = @User2ID
            WHERE cr.RoomType = 'Private' AND cr.IsActive = 1
            AND (SELECT COUNT(*) FROM ChatRoomMembers WHERE RoomID = cr.RoomID AND IsActive = 1) = 2";

        SqlParameter[] checkParams = {
            new SqlParameter("@User1ID", user1ID),
            new SqlParameter("@User2ID", user2ID)
        };

        DataTable existingRoom = DatabaseHelper.ExecuteQuery(checkQuery, checkParams);
        if (existingRoom.Rows.Count > 0)
        {
            return Convert.ToInt32(existingRoom.Rows[0]["RoomID"]);
        }

        // 获取用户信息用于创建房间名称
        string getUserQuery = @"
            SELECT u1.Username AS User1Name, u2.Username AS User2Name
            FROM Users u1, Users u2
            WHERE u1.UserID = @User1ID AND u2.UserID = @User2ID";

        SqlParameter[] getUserParams = {
            new SqlParameter("@User1ID", user1ID),
            new SqlParameter("@User2ID", user2ID)
        };

        DataTable userInfo = DatabaseHelper.ExecuteQuery(getUserQuery, getUserParams);
        if (userInfo.Rows.Count == 0) return -1;

        string roomName = $"{userInfo.Rows[0]["User1Name"]} & {userInfo.Rows[0]["User2Name"]}";

        // 创建新的私聊房间
        string createRoomQuery = @"
            INSERT INTO ChatRooms (RoomName, RoomType, Description, CreatedBy, CreatedDate, IsActive)
            VALUES (@RoomName, 'Private', '私人聊天', @CreatedBy, GETDATE(), 1);
            SELECT SCOPE_IDENTITY();";

        SqlParameter[] createParams = {
            new SqlParameter("@RoomName", roomName),
            new SqlParameter("@CreatedBy", user1ID)
        };

        object result = DatabaseHelper.ExecuteScalar(createRoomQuery, createParams);
        if (result == null || result == DBNull.Value) return -1;

        int roomID = Convert.ToInt32(result);

        // 添加两个用户到聊天室
        AddUserToRoom(roomID, user1ID);
        AddUserToRoom(roomID, user2ID);

        return roomID;
    }

    /// <summary>
    /// 将用户添加到聊天室
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    /// <param name="userID">用户ID</param>
    /// <returns>成功返回true</returns>
    public static bool AddUserToRoom(int roomID, int userID)
    {
        string query = @"
            IF NOT EXISTS (SELECT 1 FROM ChatRoomMembers WHERE RoomID = @RoomID AND UserID = @UserID)
            BEGIN
                INSERT INTO ChatRoomMembers (RoomID, UserID, JoinedDate, IsActive)
                VALUES (@RoomID, @UserID, GETDATE(), 1)
            END
            ELSE
            BEGIN
                UPDATE ChatRoomMembers 
                SET IsActive = 1, JoinedDate = GETDATE()
                WHERE RoomID = @RoomID AND UserID = @UserID
            END";

        SqlParameter[] parameters = {
            new SqlParameter("@RoomID", roomID),
            new SqlParameter("@UserID", userID)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result >= 0;
    }

    #endregion

    #region 消息管理

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    /// <param name="senderID">发送者ID</param>
    /// <param name="messageContent">消息内容</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息ID，失败返回-1</returns>
    public static int SendMessage(int roomID, int senderID, string messageContent, string messageType = "Text")
    {
        // 检查用户是否被禁言
        if (IsUserMuted(senderID, roomID))
        {
            return -2; // 表示用户被禁言
        }

        // 检查用户是否有权限发送消息到该聊天室
        if (!CanUserAccessRoom(senderID, roomID))
        {
            return -3; // 表示无权限
        }

        string query = @"
            INSERT INTO ChatMessages (RoomID, SenderID, MessageContent, MessageType, SentDate, IsDeleted)
            VALUES (@RoomID, @SenderID, @MessageContent, @MessageType, GETDATE(), 0);
            
            UPDATE ChatRooms 
            SET LastMessageDate = GETDATE() 
            WHERE RoomID = @RoomID;
            
            SELECT SCOPE_IDENTITY();";

        SqlParameter[] parameters = {
            new SqlParameter("@RoomID", roomID),
            new SqlParameter("@SenderID", senderID),
            new SqlParameter("@MessageContent", messageContent),
            new SqlParameter("@MessageType", messageType)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 获取聊天室消息
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    /// <param name="pageSize">每页消息数量</param>
    /// <param name="pageIndex">页码（从0开始）</param>
    /// <returns>消息列表</returns>
    public static DataTable GetRoomMessages(int roomID, int pageSize = 50, int pageIndex = 0)
    {
        string query = @"
            SELECT cm.MessageID, cm.RoomID, cm.SenderID, cm.MessageContent, cm.MessageType,
                   cm.SentDate, cm.IsDeleted, cm.DeletedBy, cm.DeletedDate,
                   u.Username AS SenderName, u.UserType AS SenderType
            FROM ChatMessages cm
            INNER JOIN Users u ON cm.SenderID = u.UserID
            WHERE cm.RoomID = @RoomID AND cm.IsDeleted = 0
            ORDER BY cm.SentDate DESC
            OFFSET @Offset ROWS
            FETCH NEXT @PageSize ROWS ONLY";

        SqlParameter[] parameters = {
            new SqlParameter("@RoomID", roomID),
            new SqlParameter("@PageSize", pageSize),
            new SqlParameter("@Offset", pageIndex * pageSize)
        };

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 删除消息（管理员功能）
    /// </summary>
    /// <param name="messageID">消息ID</param>
    /// <param name="deletedBy">删除者ID</param>
    /// <returns>成功返回true</returns>
    public static bool DeleteMessage(int messageID, int deletedBy)
    {
        string query = @"
            UPDATE ChatMessages
            SET IsDeleted = 1, DeletedBy = @DeletedBy, DeletedDate = GETDATE()
            WHERE MessageID = @MessageID";

        SqlParameter[] parameters = {
            new SqlParameter("@MessageID", messageID),
            new SqlParameter("@DeletedBy", deletedBy)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 获取指定时间后的新消息
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    /// <param name="lastMessageTime">最后消息时间</param>
    /// <returns>新消息数据表</returns>
    public static DataTable GetNewMessages(int roomID, DateTime lastMessageTime)
    {
        string query = @"
            SELECT cm.MessageID, cm.RoomID, cm.SenderID, cm.MessageContent, cm.MessageType,
                   cm.SentDate, cm.IsDeleted, cm.DeletedBy, cm.DeletedDate,
                   u.Username AS SenderName, u.UserType AS SenderType
            FROM ChatMessages cm
            INNER JOIN Users u ON cm.SenderID = u.UserID
            WHERE cm.RoomID = @RoomID
                AND cm.IsDeleted = 0
                AND cm.SentDate > @LastMessageTime
            ORDER BY cm.SentDate ASC";

        SqlParameter[] parameters = {
            new SqlParameter("@RoomID", roomID),
            new SqlParameter("@LastMessageTime", lastMessageTime)
        };

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 检查是否有新消息
    /// </summary>
    /// <param name="roomID">聊天室ID</param>
    /// <param name="lastMessageTime">最后消息时间</param>
    /// <returns>是否有新消息</returns>
    public static bool HasNewMessages(int roomID, DateTime lastMessageTime)
    {
        string query = @"
            SELECT COUNT(*)
            FROM ChatMessages
            WHERE RoomID = @RoomID
                AND IsDeleted = 0
                AND SentDate > @LastMessageTime";

        SqlParameter[] parameters = {
            new SqlParameter("@RoomID", roomID),
            new SqlParameter("@LastMessageTime", lastMessageTime)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        return result != null && Convert.ToInt32(result) > 0;
    }

    #endregion

    #region 权限和禁言管理

    /// <summary>
    /// 检查用户是否被禁言
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="roomID">聊天室ID（可选，null表示检查全局禁言）</param>
    /// <returns>true表示被禁言</returns>
    public static bool IsUserMuted(int userID, int? roomID = null)
    {
        string query = @"
            SELECT COUNT(*) 
            FROM ChatMuteRecords 
            WHERE UserID = @UserID 
            AND IsActive = 1 
            AND (MuteEndDate IS NULL OR MuteEndDate > GETDATE())
            AND (@RoomID IS NULL OR RoomID = @RoomID OR RoomID IS NULL)";

        SqlParameter[] parameters = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID ?? (object)DBNull.Value)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        return Convert.ToInt32(result) > 0;
    }

    /// <summary>
    /// 禁言用户
    /// </summary>
    /// <param name="userID">被禁言用户ID</param>
    /// <param name="mutedBy">执行禁言的管理员ID</param>
    /// <param name="roomID">聊天室ID（null表示全局禁言）</param>
    /// <param name="muteReason">禁言原因</param>
    /// <param name="muteEndDate">禁言结束时间（null表示永久禁言）</param>
    /// <returns>成功返回true</returns>
    public static bool MuteUser(int userID, int mutedBy, int? roomID, string muteReason, DateTime? muteEndDate)
    {
        // 先取消之前的禁言记录
        string cancelQuery = @"
            UPDATE ChatMuteRecords 
            SET IsActive = 0 
            WHERE UserID = @UserID AND (@RoomID IS NULL OR RoomID = @RoomID OR RoomID IS NULL) AND IsActive = 1";

        SqlParameter[] cancelParams = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID ?? (object)DBNull.Value)
        };

        DatabaseHelper.ExecuteNonQuery(cancelQuery, cancelParams);

        // 添加新的禁言记录
        string muteQuery = @"
            INSERT INTO ChatMuteRecords (UserID, RoomID, MutedBy, MuteReason, MuteStartDate, MuteEndDate, IsActive, CreatedDate)
            VALUES (@UserID, @RoomID, @MutedBy, @MuteReason, GETDATE(), @MuteEndDate, 1, GETDATE())";

        SqlParameter[] muteParams = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID ?? (object)DBNull.Value),
            new SqlParameter("@MutedBy", mutedBy),
            new SqlParameter("@MuteReason", muteReason ?? ""),
            new SqlParameter("@MuteEndDate", muteEndDate ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(muteQuery, muteParams);
        return result > 0;
    }

    /// <summary>
    /// 解除禁言
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="roomID">聊天室ID（null表示解除全局禁言）</param>
    /// <returns>成功返回true</returns>
    public static bool UnmuteUser(int userID, int? roomID = null)
    {
        string query = @"
            UPDATE ChatMuteRecords 
            SET IsActive = 0 
            WHERE UserID = @UserID AND (@RoomID IS NULL OR RoomID = @RoomID OR RoomID IS NULL) AND IsActive = 1";

        SqlParameter[] parameters = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 检查用户是否可以访问聊天室
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="roomID">聊天室ID</param>
    /// <returns>true表示可以访问</returns>
    public static bool CanUserAccessRoom(int userID, int roomID)
    {
        string query = @"
            SELECT cr.RoomType, 
                   CASE WHEN crm.UserID IS NOT NULL THEN 1 ELSE 0 END AS IsMember
            FROM ChatRooms cr
            LEFT JOIN ChatRoomMembers crm ON cr.RoomID = crm.RoomID AND crm.UserID = @UserID AND crm.IsActive = 1
            WHERE cr.RoomID = @RoomID AND cr.IsActive = 1";

        SqlParameter[] parameters = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID)
        };

        DataTable result = DatabaseHelper.ExecuteQuery(query, parameters);
        if (result.Rows.Count == 0) return false;

        string roomType = result.Rows[0]["RoomType"].ToString();
        bool isMember = Convert.ToBoolean(result.Rows[0]["IsMember"]);

        // 公共聊天室所有人都可以访问，私聊室只有成员可以访问
        return roomType == "Public" || isMember;
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 获取用户列表（用于创建私聊）
    /// </summary>
    /// <param name="currentUserID">当前用户ID</param>
    /// <param name="searchKeyword">搜索关键词</param>
    /// <returns>用户列表</returns>
    public static DataTable GetUsersForChat(int currentUserID, string searchKeyword = "")
    {
        string query = @"
            SELECT UserID, Username, UserType, Email
            FROM Users 
            WHERE UserID != @CurrentUserID 
            AND IsActive = 1";

        if (!string.IsNullOrEmpty(searchKeyword))
        {
            query += " AND (Username LIKE @SearchKeyword OR Email LIKE @SearchKeyword)";
        }

        query += " ORDER BY Username";

        List<SqlParameter> parameters = new List<SqlParameter>
        {
            new SqlParameter("@CurrentUserID", currentUserID)
        };

        if (!string.IsNullOrEmpty(searchKeyword))
        {
            parameters.Add(new SqlParameter("@SearchKeyword", "%" + searchKeyword + "%"));
        }

        return DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
    }

    /// <summary>
    /// 更新用户最后阅读时间
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="roomID">聊天室ID</param>
    /// <returns>成功返回true</returns>
    public static bool UpdateLastReadTime(int userID, int roomID)
    {
        string query = @"
            UPDATE ChatRoomMembers 
            SET LastReadDate = GETDATE()
            WHERE UserID = @UserID AND RoomID = @RoomID";

        SqlParameter[] parameters = {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@RoomID", roomID)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 发送系统公告到所有公共聊天室
    /// </summary>
    /// <param name="adminUserID">管理员用户ID</param>
    /// <param name="announcementContent">公告内容</param>
    /// <returns>是否发送成功</returns>
    public static bool SendSystemAnnouncement(int adminUserID, string announcementContent)
    {
        try
        {
            // 获取所有公共聊天室
            string getRoomsQuery = "SELECT RoomID FROM ChatRooms WHERE RoomType = 'Public' AND IsActive = 1";
            DataTable publicRooms = DatabaseHelper.ExecuteQuery(getRoomsQuery);

            bool allSuccess = true;
            foreach (DataRow room in publicRooms.Rows)
            {
                int roomID = Convert.ToInt32(room["RoomID"]);
                int result = SendMessage(roomID, adminUserID, announcementContent);
                if (result <= 0)
                {
                    allSuccess = false;
                }
            }

            return allSuccess;
        }
        catch (Exception)
        {
            return false;
        }
    }

    #endregion
}
