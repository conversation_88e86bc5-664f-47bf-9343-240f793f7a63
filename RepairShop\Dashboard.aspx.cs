using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_Dashboard : System.Web.UI.Page
{
    private int userID;
    private int shopID;
    private DataRow shopData;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        
        // 如果没有找到维修店信息，则创建一个默认的维修店
        if (shopID == -1)
        {
            string defaultShopName = Session["Username"] != null ? Session["Username"].ToString() + "的维修店" : "新维修店";
            shopID = ShopManager.CreateDefaultShop(userID, defaultShopName);
            
            if (shopID == -1)
            {
                // 创建失败，显示错误信息
                lblMessage.Text = "创建维修店信息失败，请联系管理员。";
                return;
            }
        }

        if (!IsPostBack)
        {
            // 加载店铺信息
            LoadShopInfo();
            // 加载统计数据
            LoadStatistics();
            // 加载待处理预约
            LoadPendingAppointments();
            // 加载系统通知
            LoadSystemNotifications();
        }
    }

    /// <summary>
    /// 加载系统通知
    /// </summary>
    private void LoadSystemNotifications()
    {
        DataTable notifications = AdminManager.GetActiveSystemNotifications();
        rptNotifications.DataSource = notifications;
        rptNotifications.DataBind();
    }

    /// <summary>
    /// 加载店铺信息
    /// </summary>
    private void LoadShopInfo()
    {
        DataTable shopTable = ShopManager.GetShopByID(shopID);
        if (shopTable != null && shopTable.Rows.Count > 0)
        {
            shopData = shopTable.Rows[0];
            lblShopName.Text = shopData["ShopName"].ToString();
            lblAddress.Text = shopData["Address"].ToString();
            lblBusinessHours.Text = shopData["BusinessHours"] != DBNull.Value ? shopData["BusinessHours"].ToString() : "未设置";
            lblContactPerson.Text = shopData["ContactPerson"] != DBNull.Value ? shopData["ContactPerson"].ToString() : "未设置";
            if (shopData["Rating"] != DBNull.Value)
            {
                decimal rating = Convert.ToDecimal(shopData["Rating"]);
                lblRating.Text = string.Format("{0:F1}", rating);
            }
            else
            {
                lblRating.Text = "暂无评分";
            }
            
            // 显示店铺照片
            string photoUrl = null;
            if (shopData["PhotoUrl"] != DBNull.Value && !string.IsNullOrEmpty(shopData["PhotoUrl"].ToString()))
            {
                photoUrl = shopData["PhotoUrl"].ToString();
                System.Diagnostics.Debug.WriteLine($"从数据库获取到的图片URL: {photoUrl}");
                
                try
                {
                    // 处理相对路径和绝对路径
                    string physicalPath;
                    if (photoUrl.StartsWith("~"))
                    {
                        physicalPath = Server.MapPath(photoUrl);
                    }
                    else if (photoUrl.StartsWith("/"))
                    {
                        physicalPath = Server.MapPath("~" + photoUrl);
                    }
                    else
                    {
                        physicalPath = Server.MapPath("~/" + photoUrl);
                    }
                    
                    if (System.IO.File.Exists(physicalPath))
                    {
                        // 确保URL格式一致
                        if (photoUrl.StartsWith("/"))
                        {
                            imgShopPhoto.ImageUrl = "~" + photoUrl;
                        }
                        else if (!photoUrl.StartsWith("~"))
                        {
                            imgShopPhoto.ImageUrl = "~/" + photoUrl;
                        }
                        else
                        {
                            imgShopPhoto.ImageUrl = photoUrl;
                        }
                        System.Diagnostics.Debug.WriteLine($"图片文件存在，设置URL: {imgShopPhoto.ImageUrl}");
                    }
                    else
                    {
                        // 如果图片文件不存在，使用默认图片
                        imgShopPhoto.ImageUrl = "~/Images/default-shop.png";
                        System.Diagnostics.Debug.WriteLine($"图片文件不存在: {physicalPath}，使用默认图片");
                    }
                }
                catch (Exception ex)
                {
                    // 如果出现异常，使用默认图片
                    imgShopPhoto.ImageUrl = "~/Images/default-shop.png";
                    System.Diagnostics.Debug.WriteLine($"处理图片路径时出错: {ex.Message}");
                }
            }
            else
            {
                // 如果没有图片URL，使用默认图片
                imgShopPhoto.ImageUrl = "~/Images/default-shop.png";
                System.Diagnostics.Debug.WriteLine("无图片URL，使用默认图片");
            }
        }
    }

    /// <summary>
    /// 加载统计数据
    /// </summary>
    private void LoadStatistics()
    {
        DataTable statsTable = ShopManager.GetShopStatistics(shopID);
        if (statsTable != null && statsTable.Rows.Count > 0)
        {
            DataRow statsRow = statsTable.Rows[0];

            // 显示统计信息
            lblPendingAppointments.Text = Convert.ToString(statsRow["PendingAppointments"]);
            lblConfirmedAppointments.Text = Convert.ToString(statsRow["ConfirmedAppointments"]);
            lblCompletedOrders.Text = Convert.ToString(statsRow["CompletedAppointments"]);
            lblTotalServices.Text = Convert.ToString(statsRow["TotalServices"]);
            lblTotalReviews.Text = Convert.ToString(statsRow["TotalReviews"]);

            // 加载员工数量
            DataTable employeesTable = ShopManager.GetActiveEmployees(shopID);
            lblTotalEmployees.Text = employeesTable.Rows.Count.ToString();

            if (statsRow["AverageRating"] != DBNull.Value)
            {
                decimal avgRating = Convert.ToDecimal(statsRow["AverageRating"]);
                lblAverageRating.Text = string.Format("{0:F1}", avgRating);
            }
            else
            {
                lblAverageRating.Text = "0.0";
            }

            if (statsRow["TotalRevenue"] != DBNull.Value)
            {
                decimal totalRevenue = Convert.ToDecimal(statsRow["TotalRevenue"]);
                lblTotalRevenue.Text = string.Format("¥{0:N2}", totalRevenue);
            }
            else
            {
                lblTotalRevenue.Text = "¥0.00";
            }
        }
    }

    /// <summary>
    /// 加载待处理预约
    /// </summary>
    private void LoadPendingAppointments()
    {
        DataTable appointmentsTable = ShopManager.GetPendingAppointments(shopID);
        gvPendingAppointments.DataSource = appointmentsTable;
        gvPendingAppointments.DataBind();
    }

    /// <summary>
    /// 编辑店铺信息链接按钮点击事件
    /// </summary>
    protected void lbtnEditShop_Click(object sender, EventArgs e)
    {
        pnlShopInfo.Visible = false;
        pnlEditShop.Visible = true;
        
        // 如果shopData为空或已过期，重新加载数据
        if (shopData == null)
        {
            LoadShopInfo();
            if (shopData == null)
            {
                txtShopName.Text = string.Empty;
                txtDescription.Text = string.Empty;
                txtAddress.Text = string.Empty;
                txtBusinessHours.Text = string.Empty;
                txtContactPerson.Text = string.Empty;
                lblMessage.Text = "无法加载店铺信息，请保存后重试。";
                return;
            }
        }
        
        // 填充文本框
        txtShopName.Text = shopData["ShopName"].ToString();
        txtDescription.Text = shopData["Description"] != DBNull.Value ? shopData["Description"].ToString() : string.Empty;
        txtAddress.Text = shopData["Address"].ToString();
        txtBusinessHours.Text = shopData["BusinessHours"] != DBNull.Value ? shopData["BusinessHours"].ToString() : string.Empty;
        txtContactPerson.Text = shopData["ContactPerson"] != DBNull.Value ? shopData["ContactPerson"].ToString() : string.Empty;
        
        // 加载经纬度
        if (shopData.Table.Columns.Contains("Longitude") && shopData["Longitude"] != DBNull.Value)
        {
            hfLongitude.Value = shopData["Longitude"].ToString();
        }
        if (shopData.Table.Columns.Contains("Latitude") && shopData["Latitude"] != DBNull.Value)
        {
            hfLatitude.Value = shopData["Latitude"].ToString();
        }
        
        // 显示图片预览 - 优先使用控件中已有的图片URL，这样在多次编辑时不会丢失之前的修改
        if (!string.IsNullOrEmpty(imgShopPhoto.ImageUrl) && !imgShopPhoto.ImageUrl.EndsWith("default-shop.png"))
        {
            imgShopPhotoPreview.ImageUrl = imgShopPhoto.ImageUrl;
            System.Diagnostics.Debug.WriteLine($"使用当前显示的图片URL: {imgShopPhoto.ImageUrl}");
        }
        else if (shopData["PhotoUrl"] != DBNull.Value && !string.IsNullOrEmpty(shopData["PhotoUrl"].ToString()))
        {
            string photoUrl = shopData["PhotoUrl"].ToString();
            
            try
            {
                // 处理相对路径和绝对路径
                string physicalPath;
                if (photoUrl.StartsWith("~"))
                {
                    physicalPath = Server.MapPath(photoUrl);
                }
                else if (photoUrl.StartsWith("/"))
                {
                    physicalPath = Server.MapPath("~" + photoUrl);
                }
                else
                {
                    physicalPath = Server.MapPath("~/" + photoUrl);
                }
                
                if (System.IO.File.Exists(physicalPath))
                {
                    // 确保URL格式一致
                    if (photoUrl.StartsWith("/"))
                    {
                        imgShopPhotoPreview.ImageUrl = "~" + photoUrl;
                    }
                    else if (!photoUrl.StartsWith("~"))
                    {
                        imgShopPhotoPreview.ImageUrl = "~/" + photoUrl;
                    }
                    else
                    {
                        imgShopPhotoPreview.ImageUrl = photoUrl;
                    }
                    System.Diagnostics.Debug.WriteLine($"使用数据库中的图片URL: {photoUrl}");
                }
                else
                {
                    // 如果图片文件不存在，使用默认图片
                    imgShopPhotoPreview.ImageUrl = "~/Images/default-shop.png";
                    System.Diagnostics.Debug.WriteLine($"图片文件不存在: {physicalPath}，使用默认图片");
                }
            }
            catch (Exception ex)
            {
                // 如果出现异常，使用默认图片
                imgShopPhotoPreview.ImageUrl = "~/Images/default-shop.png";
                System.Diagnostics.Debug.WriteLine($"处理图片路径时出错: {ex.Message}");
            }
        }
        else
        {
            // 如果没有图片URL，使用默认图片
            imgShopPhotoPreview.ImageUrl = "~/Images/default-shop.png";
            System.Diagnostics.Debug.WriteLine("无图片URL，使用默认图片");
        }
    }

    /// <summary>
    /// 保存店铺信息按钮点击事件
    /// </summary>
    protected void btnSaveShopInfo_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
        {
            return;
        }
        string shopName = txtShopName.Text.Trim();
        string description = txtDescription.Text.Trim();
        string address = txtAddress.Text.Trim();
        string businessHours = txtBusinessHours.Text.Trim();
        string contactPerson = txtContactPerson.Text.Trim();
        
        // 获取当前图片URL，优先使用预览图的URL，这样可以保持最近一次修改的图片
        string photoUrl = null;
        if (!string.IsNullOrEmpty(imgShopPhotoPreview.ImageUrl))
        {
            photoUrl = imgShopPhotoPreview.ImageUrl;
            // 如果URL以~开头，去掉~
            if (photoUrl.StartsWith("~"))
            {
                photoUrl = photoUrl.Substring(1);
            }
            System.Diagnostics.Debug.WriteLine($"使用预览图URL: {photoUrl}");
        }
        else if (shopData != null && shopData["PhotoUrl"] != DBNull.Value)
        {
            photoUrl = shopData["PhotoUrl"].ToString();
            System.Diagnostics.Debug.WriteLine($"使用数据库图片URL: {photoUrl}");
        }
        
        // 获取经纬度
        decimal? longitude = null;
        decimal? latitude = null;
        
        if (!string.IsNullOrEmpty(hfLongitude.Value) && !string.IsNullOrEmpty(hfLatitude.Value))
        {
            if (decimal.TryParse(hfLongitude.Value, out decimal lon) && decimal.TryParse(hfLatitude.Value, out decimal lat))
            {
                longitude = lon;
                latitude = lat;
            }
        }
        
        // 只有当用户上传了新图片时才更新图片URL
        if (fuShopPhoto.HasFile && fuShopPhoto.PostedFile.ContentLength > 0)
        {
            try
            {
                string ext = System.IO.Path.GetExtension(fuShopPhoto.FileName).ToLower();
                // 检查文件类型
                if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif")
                {
                    string fileName = Guid.NewGuid().ToString() + ext;
                    string dir = Server.MapPath("~/Images/Shops/");
                    if (!System.IO.Directory.Exists(dir)) System.IO.Directory.CreateDirectory(dir);
                    string savePath = dir + fileName;
                    fuShopPhoto.SaveAs(savePath);
                    photoUrl = "/Images/Shops/" + fileName;
                    System.Diagnostics.Debug.WriteLine($"新上传的图片URL: {photoUrl}");
                }
                else
                {
                    lblMessage.Text = "只支持JPG、PNG和GIF格式的图片。";
                    return;
                }
            }
            catch (Exception ex)
            {
                lblMessage.Text = "上传图片时出错: " + ex.Message;
                System.Diagnostics.Debug.WriteLine($"上传图片时出错: {ex.Message}");
                return;
            }
        }
        else
        {
            System.Diagnostics.Debug.WriteLine("未上传新图片，保留当前图片URL: " + photoUrl);
        }
        
        // 如果没有图片URL（包括原有的和新上传的），使用默认图片
        if (string.IsNullOrEmpty(photoUrl))
        {
            photoUrl = "/Images/default-shop.png";
            System.Diagnostics.Debug.WriteLine("使用默认图片URL");
        }
        
        bool success = ShopManager.UpdateShopInfo(shopID, shopName, description, address, businessHours, contactPerson, photoUrl, longitude, latitude);
        if (success)
        {
            lblMessage.Text = "店铺信息保存成功！";
            pnlEditShop.Visible = false;
            pnlShopInfo.Visible = true;
            // 重新加载店铺信息，确保显示最新数据
            shopData = null; // 清空缓存的数据
            LoadShopInfo();
        }
        else
        {
            lblMessage.Text = "保存失败，请稍后再试。";
        }
    }

    /// <summary>
    /// 取消编辑按钮点击事件
    /// </summary>
    protected void btnCancelEdit_Click(object sender, EventArgs e)
    {
        // 切换回显示面板
        pnlEditShop.Visible = false;
        pnlShopInfo.Visible = true;
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvPendingAppointments_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int appointmentID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "ConfirmAppointment")
        {
            // 确认预约
            bool success = ShopManager.ConfirmAppointment(appointmentID);
            if (success)
            {
                // 重新加载数据
                LoadPendingAppointments();
                LoadStatistics();
                lblMessage.Text = "预约已确认。";
            }
            else
            {
                lblMessage.Text = "确认失败，请稍后再试。";
            }
        }
        else if (e.CommandName == "ViewAppointment")
        {
            // 查看预约详情
            Response.Redirect("~/RepairShop/Appointments.aspx?id=" + appointmentID);
        }
    }
} 